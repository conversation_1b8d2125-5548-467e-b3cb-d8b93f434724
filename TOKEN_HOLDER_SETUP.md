# Token Holder Fetcher Setup Guide

This feature allows you to fetch token holder lists from CronoScan directly through the admin panel.

## Features

- **Manual Token Holder Fetching**: Fetch holder lists on-demand from the admin panel
- **Database Storage**: Store historical snapshots of token holder data
- **CSV Export**: Download holder data as CSV files
- **Top Holders Display**: View top 10 holders with balances
- **Multi-Chain Support**: Works with any chain supported by your application

## Installation

### 1. Install Dependencies

```bash
# Make the installation script executable
chmod +x install-token-holder-deps.sh

# Run the installation script
./install-token-holder-deps.sh
```

Or install manually:

```bash
npm install puppeteer csv-parser
```

### 2. Install System Dependencies (Ubuntu/Debian)

```bash
sudo apt update
sudo apt install -y chromium-browser fonts-liberation libasound2 libatk-bridge2.0-0 libatk1.0-0 libatspi2.0-0 libcups2 libdbus-1-3 libdrm2 libgtk-3-0 libnspr4 libnss3 libxcomposite1 libxdamage1 libxfixes3 libxkbcommon0 libxrandr2 xvfb
```

### 3. Run Database Migration

```bash
# Connect to your MySQL database and run the migration
mysql -u your_username -p your_database_name < db/migrations/add_token_holder_tables.sql
```

### 4. Restart Application

```bash
pm2 restart all
```

## Usage

### Admin Panel Access

1. Go to your admin panel: `https://yourdomain.com/admin`
2. Connect your wallet (must be an admin user)
3. Click on the "Token Holders" tab

### Fetching Token Holders

1. **Select Chain**: Make sure you have selected the correct chain (not "all")
2. **Enter Contract Address**: Input the token contract address (e.g., `******************************************`)
3. **Token Name** (Optional): Enter a friendly name for the token
4. **Click "Fetch Token Holders"**: The system will scrape CronoScan and store the data

### Viewing Results

After fetching, you'll see:

- **Total Holders**: Number of unique holders
- **Token Name**: Name of the token
- **Snapshot Date**: When the data was fetched
- **Top 10 Holders**: List with addresses and balances

### Downloading Data

Click the "Download CSV" button to get a CSV file with all holder data in the format:

```csv
HolderAddress,Balance,PendingBalanceUpdate
"******************************************","268,757,276.839081698094805521","No"
```

## Database Schema

The feature adds three new tables:

### `token_holder_snapshots`

- Stores metadata about each fetch operation
- Tracks total holders, date, chain, contract address
- Supports multichain addresses (Ethereum 42 chars, Solana 44 chars)

### `token_holders`

- Stores individual holder records
- Links to snapshots via foreign key
- Stores both original balance string and numeric version
- Supports multichain holder addresses

### `token_holder_jobs`

- Tracks the status of fetch operations
- Useful for debugging and monitoring
- Supports multichain contract addresses and chain IDs

## Multichain Support

The tables support both Ethereum-style addresses (42 characters) and Solana addresses (44 characters):

- `contract_address` and `holder_address` fields: varchar(50)
- `chain_id` field: varchar(40) to support longer Solana chain identifiers

## Migration for Existing Installations

If you have existing token holder tables, run the migration to update field lengths:

```bash
# Using the migration script
node scripts/migrate-token-holder-tables.js

# Or manually run the SQL migration
mysql -u your_user -p your_database < db/migrations/update_token_holder_address_lengths.sql
```

## API Endpoints

### Fetch Token Holders

```
POST /api/admin/token-holders/fetch
Body: {
  "contractAddress": "0x...",
  "chainId": "25",
  "tokenName": "Web3Tools"
}
```

### Get Token Holder Data

```
GET /api/admin/token-holders/{contractAddress}?chainId=25&includeHolders=true
```

### Export CSV

```
POST /api/admin/token-holders/{contractAddress}
Body: {
  "chainId": "25",
  "format": "csv"
}
```

## Troubleshooting

### Puppeteer Issues

- Make sure all system dependencies are installed
- Check that Chromium is available: `which chromium-browser`
- Verify memory availability (Puppeteer needs ~100MB RAM)

### CronoScan Access Issues

- The tool uses the same export URL as the web interface
- If CronoScan changes their format, the parser may need updates
- Rate limiting: Wait between requests if fetching multiple tokens

### Database Issues

- Ensure the migration was run successfully
- Check that the foreign key constraints are working
- Verify table permissions for your database user

## Example Usage

1. **Web3Tools Token**: `******************************************`
2. **WCRO Token**: `0x5c7f8a570d578ed84e63fdfa7b1ee72deae1ae23`
3. **Any ERC-20 on Cronos**: Just use the contract address

## Security Notes

- Only admin users can access this feature
- The tool runs server-side to avoid CORS issues
- No sensitive data is exposed in the frontend
- All database operations use proper foreign key constraints

## Additional Features

Current system capabilities:

- Historical comparison between snapshots
- Integration with token requirements system
- Support for multiple blockchain explorers
- Manual CSV upload as fallback when automatic fetching fails
