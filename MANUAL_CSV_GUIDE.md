# Manual CSV Upload Guide for Token Holders

Download the CSV file from the appropriate blockchain explorer and upload it through the admin panel. The system supports all major blockchain networks and address formats.

## Supported Chains & Instructions

### Ethereum Mainnet (Chain ID: 1)

1. Go to: `https://etherscan.io/exportData?type=tokenholders&contract=YOUR_CONTRACT_ADDRESS&decimal=18`
2. Replace `YOUR_CONTRACT_ADDRESS` with the actual token contract address
3. Example: `https://etherscan.io/exportData?type=tokenholders&contract=******************************************&decimal=18`

### Cronos (Chain ID: 25)

1. Go to: `https://cronoscan.com/exportData?type=tokenholders&contract=YOUR_CONTRACT_ADDRESS&decimal=18`
2. Replace `YOUR_CONTRACT_ADDRESS` with the actual token contract address
3. Example: `https://cronoscan.com/exportData?type=tokenholders&contract=******************************************&decimal=18`

### Polygon (Chain ID: 137)

1. Go to: `https://polygonscan.com/exportData?type=tokenholders&contract=YOUR_CONTRACT_ADDRESS&decimal=18`
2. Replace `YOUR_CONTRACT_ADDRESS` with the actual token contract address

### Arbitrum (Chain ID: 42161)

1. Go to: `https://arbiscan.io/exportData?type=tokenholders&contract=YOUR_CONTRACT_ADDRESS&decimal=18`
2. Replace `YOUR_CONTRACT_ADDRESS` with the actual token contract address

### BNB Smart Chain (Chain ID: 56)

1. Go to: `https://bscscan.com/exportData?type=tokenholders&contract=YOUR_CONTRACT_ADDRESS&decimal=18`
2. Replace `YOUR_CONTRACT_ADDRESS` with the actual token contract address

### Optimism (Chain ID: 10)

1. Go to: `https://optimistic.etherscan.io/exportData?type=tokenholders&contract=YOUR_CONTRACT_ADDRESS&decimal=18`
2. Replace `YOUR_CONTRACT_ADDRESS` with the actual token contract address

### Avalanche (Chain ID: 43114)

1. Go to: `https://snowtrace.io/exportData?type=tokenholders&contract=YOUR_CONTRACT_ADDRESS&decimal=18`
2. Replace `YOUR_CONTRACT_ADDRESS` with the actual token contract address

### Fantom (Chain ID: 250)

1. Go to: `https://ftmscan.com/exportData?type=tokenholders&contract=YOUR_CONTRACT_ADDRESS&decimal=18`
2. Replace `YOUR_CONTRACT_ADDRESS` with the actual token contract address

### Base (Chain ID: 8453)

1. Go to: `https://basescan.org/exportData?type=tokenholders&contract=YOUR_CONTRACT_ADDRESS&decimal=18`
2. Replace `YOUR_CONTRACT_ADDRESS` with the actual token contract address

### Solana (Chain ID: sol1)

1. Go to: `https://etherscan.io/exportData?type=tokenholders&contract=YOUR_CONTRACT_ADDRESS&decimal=18`
2. Replace `YOUR_CONTRACT_ADDRESS` with the actual token contract address
3. Example: `https://etherscan.io/exportData?type=tokenholders&contract=******************************************&decimal=18`

### Arbitrum (Chain ID: 42161)

1. Go to: `https://arbiscan.io/exportData?type=tokenholders&contract=YOUR_CONTRACT_ADDRESS&decimal=18`
2. Replace `YOUR_CONTRACT_ADDRESS` with the actual token contract address
3. Example: `https://arbiscan.io/exportData?type=tokenholders&contract=******************************************&decimal=18`

### Sepolia Testnet (Chain ID: 11155111)

1. Go to: `https://sepolia.etherscan.io/exportData?type=tokenholders&contract=YOUR_CONTRACT_ADDRESS&decimal=18`
2. Replace `YOUR_CONTRACT_ADDRESS` with the actual token contract address
3. Example: `https://sepolia.etherscan.io/exportData?type=tokenholders&contract=******************************************&decimal=18`

### Solana (Chain ID: sol1)

1. Go to: `https://solscan.io/token/YOUR_TOKEN_ADDRESS#holders`
2. Replace `YOUR_TOKEN_ADDRESS` with the actual Solana token address
3. Example: `https://solscan.io/token/CzHc1ugMNhim5JCJC8ebbp4k14jfrbZx1HNcMyEppump#holders`
4. Look for the "export.csv" button on the page

### Cronos zkEVM (Chain ID: 388)

1. Go to: `https://explorer.cronos.org/zkevm/token/YOUR_CONTRACT_ADDRESS/token-holders`
2. Replace `YOUR_CONTRACT_ADDRESS` with the actual token contract address
3. Look for export functionality on the page

## General Steps

### 2. Download the CSV File

1. Wait for the page to load (may take a few seconds due to Cloudflare)
2. Look for a "Download" button or link on the page
3. Click the download button to save the CSV file to your computer
4. The file will typically be named something like `export-tokenholders.csv`

### 3. Upload via Admin Panel

1. Open your admin panel: `http://localhost:3000/admin`
2. Go to the "Token Holders" tab
3. Enter the contract address and token name
4. In the "Manual CSV Upload" section:
   - Click "Choose File" and select your downloaded CSV
   - Click "Upload CSV"
5. The system will process the file and display the results

## CSV Format Requirements

The CSV file should have the following format:

```csv
HolderAddress,Balance,PendingBalanceUpdate
"******************************************","268,757,276.839081698094805521","No"
"******************************************","123,456.789","No"
```

### Required Columns:

- **HolderAddress**: The wallet address of the token holder
- **Balance**: The token balance (can include commas)
- **PendingBalanceUpdate**: Usually "Yes" or "No"

## Troubleshooting

### Common Issues:

1. **"CSV file does not appear to contain valid token holder data"**

   - Make sure the CSV has the correct headers
   - Check that the file isn't corrupted or empty

2. **"No token holders found in the CSV file"**

   - Verify the CSV has data rows (not just headers)
   - Check that the format matches the expected structure

3. **"Please upload a CSV file"**
   - Make sure you selected a file with `.csv` extension
   - Try saving the file again from the blockchain explorer

### Alternative Download Methods:

If the standard export page doesn't work, try these alternative URLs:

**For Cronos:**

- `https://cronoscan.com/token/tokenholderchart/downloadcsv?contract=CONTRACT_ADDRESS`
- `https://cronoscan.com/token/generic-tokenholders2?contract=CONTRACT_ADDRESS&a=download`

**For Ethereum:**

- `https://etherscan.io/token/tokenholderchart/downloadcsv?contract=CONTRACT_ADDRESS`
- `https://etherscan.io/token/generic-tokenholders2?contract=CONTRACT_ADDRESS&a=download`

**For Arbitrum:**

- `https://arbiscan.io/token/tokenholderchart/downloadcsv?contract=CONTRACT_ADDRESS`
- `https://arbiscan.io/token/generic-tokenholders2?contract=CONTRACT_ADDRESS&a=download`

**For Sepolia:**

- `https://sepolia.etherscan.io/token/tokenholderchart/downloadcsv?contract=CONTRACT_ADDRESS`
- `https://sepolia.etherscan.io/token/generic-tokenholders2?contract=CONTRACT_ADDRESS&a=download`

## Features After Upload

Once uploaded successfully, you can:

- ✅ View total holder count and snapshot date
- ✅ See top 10 holders with balances
- ✅ Download the processed CSV file
- ✅ View individual holder addresses on CronoScan
- ✅ Track historical snapshots over time

## Security Notes

- The CSV file is processed server-side and stored securely
- Only wallet addresses and balances are stored
- No private keys or sensitive data should be in the CSV
- Files are validated before processing

## Need Help?

If you continue to have issues:

1. Check that the token contract address is correct
2. Verify the token exists on CronoScan
3. Try downloading the CSV at different times (CronoScan may have rate limits)
4. Contact support if the issue persists
