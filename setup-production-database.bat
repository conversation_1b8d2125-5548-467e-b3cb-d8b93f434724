@echo off
setlocal enabledelayedexpansion

REM Production Database Setup Script for Web3Socials
REM For Hostinger VPS with CloudPanel and MySQL
REM This script will set up a fresh database with all required tables

echo.
echo ========================================
echo 🚀 Web3Socials Production Database Setup
echo ========================================
echo.

REM Check if .env file exists
if not exist ".env" (
    echo [ERROR] .env file not found!
    echo Please create a .env file with your database configuration:
    echo.
    echo DATABASE_URL=mysql://username:password@localhost:3306/database_name
    echo MYSQL_SSL=false
    echo NEXT_PUBLIC_PROJECT_ID=your_reown_project_id
    echo NODE_ENV=production
    pause
    exit /b 1
)

echo [INFO] ✅ .env file found

REM Check if Node.js and npm are available
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] Node.js is not installed!
    pause
    exit /b 1
)

where npm >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] npm is not installed!
    pause
    exit /b 1
)

echo [INFO] ✅ Node.js and npm are available

REM Install dependencies if node_modules doesn't exist
if not exist "node_modules" (
    echo [INFO] 📦 Installing dependencies...
    call npm install
    if %errorlevel% neq 0 (
        echo [ERROR] Failed to install dependencies!
        pause
        exit /b 1
    )
    echo [SUCCESS] Dependencies installed
) else (
    echo [INFO] ✅ Dependencies already installed
)

REM Confirm database reset
echo.
echo [WARNING] ⚠️  WARNING: This will DELETE ALL DATA in your database!
echo [WARNING] This action cannot be undone.
echo.
set /p confirm="Are you sure you want to proceed with database reset? (y/N): "
if /i not "%confirm%"=="y" (
    echo [INFO] Operation cancelled by user
    pause
    exit /b 0
)

echo.
echo [INFO] 🗄️  Resetting database and creating all tables...
call npm run reset-database
if %errorlevel% neq 0 (
    echo [ERROR] Database reset failed!
    pause
    exit /b 1
)

echo [SUCCESS] Database reset completed successfully!

echo.
echo [INFO] 🔧 Verifying chain defaults...
call npm run verify-chain-defaults
if %errorlevel% neq 0 (
    echo [WARNING] Chain defaults verification had issues - check output above
)

echo.
echo [INFO] ✅ Checking all tables...
call npm run check-tables

echo.
echo [SUCCESS] 🎉 Production database setup completed successfully!
echo ========================================
echo [INFO] 📋 Summary of created tables:
echo    • system_settings - System configuration
echo    • web3Profile - User profiles
echo    • waitingList - User waiting list
echo    • componentPositions - Component layouts
echo    • componentImages - Image storage
echo    • profileLikes - Like system
echo    • profileReferrals - Referral system
echo    • token_holder_snapshots - Token holder metadata
echo    • token_holders - Token holder data
echo    • token_holder_jobs - Token fetch jobs
echo    • balance_check_logs - Balance check logs
echo.
echo [INFO] 🚀 Your database is ready for production!
echo [INFO] You can now start your application with: npm run build ^&^& npm start
echo.
pause
