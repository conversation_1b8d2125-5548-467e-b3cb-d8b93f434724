# Fix: Data too long for column 'contract_address' at row 1

This error occurs when trying to upload token holder CSV files with contract addresses longer than the current database column limit.

## Problem

The database schema currently has `varchar(50)` for contract addresses, but some blockchain addresses (especially Solana addresses) can be up to 44 characters long, and with additional formatting or longer addresses from other chains, this can exceed the 50-character limit.

## Solution

### Step 1: Run Database Migration

You need to increase the column length in your database. Choose one of these methods:

#### Option A: Using the Migration Script (Recommended)

```bash
# Install mysql2 if not already installed
npm install mysql2

# Run the migration script
node scripts/fix-contract-address-length.js
```

#### Option B: Manual SQL Execution

Connect to your MySQL database and run:

```sql
-- Update token_holder_snapshots table
ALTER TABLE `token_holder_snapshots` 
MODIFY COLUMN `contract_address` varchar(70) NOT NULL;

-- Update token_holders table  
ALTER TABLE `token_holders`
MODIFY COLUMN `holder_address` varchar(70) NOT NULL;

-- Update token_holder_jobs table
ALTER TABLE `token_holder_jobs`
MODIFY COLUMN `contract_address` varchar(70) NOT NULL;
```

### Step 2: Verify the Fix

1. After running the migration, try uploading your Solana CSV file again
2. The system should now accept longer contract addresses
3. Check the admin panel to confirm the data was processed correctly

## Solana CSV Format Support

The system now supports Solana CSV format with these columns:
- `Account` → Maps to HolderAddress
- `Token Account` → Ignored (not needed)
- `Quantity` → Maps to Balance
- `Percentage` → Ignored (not needed)

## Testing

Use the provided `sample-solana-token-holders.csv` file to test the functionality:

1. Go to your admin panel: `/admin`
2. Navigate to the "Token Holders" tab
3. Enter a Solana contract address (e.g., `7hB4AgnZp7RiThvtFTGMedkrU3G8X34fwHzy1vQjpump`)
4. Select chain: `sol1` (Solana)
5. Upload the sample CSV file
6. Verify it processes successfully

## Supported Address Formats

The system now supports:
- **Ethereum/EVM chains**: 42 characters (0x + 40 hex chars)
- **Solana**: 32-44 characters (base58 format)
- **Other chains**: Up to 70 characters total

## Troubleshooting

If you still get the error after running the migration:

1. **Check if migration ran successfully**: Connect to your database and verify the column lengths:
   ```sql
   DESCRIBE token_holder_snapshots;
   DESCRIBE token_holders;
   DESCRIBE token_holder_jobs;
   ```

2. **Restart your application**: After database changes, restart your Next.js application:
   ```bash
   npm run dev
   ```

3. **Check contract address length**: Ensure your contract address is not longer than 70 characters

4. **Verify CSV format**: Make sure your CSV has the correct headers and format

## Prevention

This fix increases the column lengths to 70 characters, which should accommodate:
- Current blockchain address formats
- Future blockchain addresses
- Any additional formatting that might be needed

The migration is backward compatible and won't affect existing data.
