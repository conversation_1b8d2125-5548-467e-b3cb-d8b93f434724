'use client';

import { useEffect, useRef } from 'react';
import { useAppKitNetwork, useAppKitAccount  } from '@reown/appkit/react'

interface AppKitButtonProps {
  className?: string;
}

export default function AppKitButton({ className }: AppKitButtonProps) {
  const buttonRef = useRef<HTMLDivElement>(null);
   const { chainId } = useAppKitNetwork()
   const { isConnected } = useAppKitAccount() // AppKit hook to get the address and check if the user is connected

  // Set the chain attribute on the appkit-button element
  useEffect(() => {
    if (buttonRef.current && chainId) {
      buttonRef.current.setAttribute('chain', chainId.toString());
    }
  }, [chainId]);



  return <div ref={buttonRef} className={className}><appkit-button /></div>;
}
