'use client';

import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAppKitAccount } from '@reown/appkit/react';
import { Loader2 } from 'lucide-react';

export default function ProfileStatusChecker() {
  const router = useRouter();
  const pathname = usePathname();
  const { address } = useAppKitAccount();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkProfileStatus = async () => {
      // ONLY check direct profile URLs (e.g., /nameoraddress)
      // Skip ALL other routes - no status checking for application pages

      // Check if this is a direct profile access (just /address or /name with no other path segments)
      const isDirectProfileAccess =
        pathname &&
        pathname !== '/' &&
        pathname.indexOf('/', 1) === -1 && // No second slash
        !pathname.startsWith('/api') &&
        !pathname.startsWith('/_next') &&
        !pathname.startsWith('/admin') &&
        !pathname.startsWith('/profile-error') &&
        !pathname.startsWith('/create') &&
        !pathname.startsWith('/edit') &&
        !pathname.startsWith('/view') &&
        !pathname.startsWith('/layout') &&
        !pathname.startsWith('/discover') &&
        !pathname.includes('.') && // Skip static files
        !pathname.startsWith('/dashboard');

      console.log('[ProfileStatusChecker] Checking path:', pathname);
      console.log('[ProfileStatusChecker] Is direct profile access:', isDirectProfileAccess);

      if (!isDirectProfileAccess) {
        // Not a direct profile access, skip checking
        console.log('[ProfileStatusChecker] Not a direct profile access, skipping check');
        setIsChecking(false);
        return;
      }

      try {
        // Extract the profile identifier from the URL (the part after the /)
        const profileIdentifier = pathname.substring(1);
        console.log('[ProfileStatusChecker] Checking profile:', profileIdentifier);

        // Fetch profile status
        const response = await fetch(`/api/profile/check-status?identifier=${profileIdentifier}`);
        const data = await response.json();
        console.log('[ProfileStatusChecker] Profile status data:', data);

        // Allow access if:
        // 1. Profile is approved
        // 2. Profile is not found (let the page handle it)
        // We no longer make an exception for the profile owner
        const isOwnProfile = address && data.address && address.toLowerCase() === data.address.toLowerCase();
        console.log('[ProfileStatusChecker] Is own profile:', isOwnProfile);

        if (data.status !== 'approved' && data.status !== 'not-found') {
          // Redirect all non-approved profiles, even if it's the user's own profile
          console.log('[ProfileStatusChecker] Redirecting to error page for status:', data.status);
          // Use window.location for a hard redirect instead of router.push
          // Use the name parameter instead of address
          const nameParam = data.name ? `&name=${data.name}` : '';
          window.location.href = `/profile-error?status=${data.status}${nameParam}`;
          return; // Don't set isChecking to false, we're redirecting
        } else {
          console.log('[ProfileStatusChecker] Profile access allowed');
          setIsChecking(false);
        }
      } catch (error) {
        console.error('Error checking profile status:', error);
        setIsChecking(false);
      }
    };

    checkProfileStatus();
  }, [pathname, router, address]);

  if (!isChecking) {
    return null;
  }

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-white/80 dark:bg-black/80 z-50">
      <div className="flex flex-col items-center">
        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
        <p className="mt-2 text-sm text-gray-600 dark:text-gray-300">Checking profile status...</p>
      </div>
    </div>
  );
}
