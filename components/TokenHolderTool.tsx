'use client';

import { useState } from 'react';
import { toast } from 'sonner';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Download,
  Loader2,
  Users,
  Calendar,
  ExternalLink,
  Upload,
  FileText
} from 'lucide-react';

interface TokenHolderToolProps {
  selectedChainId: string;
}

interface TokenSnapshot {
  id: number;
  tokenName: string | null;
  snapshotDate: string;
  totalHolders: number;
  createdAt: string;
}

interface TokenHolder {
  holderAddress: string;
  balance: string;
  balanceNumeric: string | null;
  pendingBalanceUpdate: string | null;
}

// Chain-specific explorer configurations
const EXPLORER_CONFIGS = {
  // Ethereum and testnets
  '1': { name: 'Etherscan', url: (address: string) => `https://etherscan.io/exportData?type=tokenholders&contract=${address}&decimal=18` },
  '11155111': { name: 'Sepolia Etherscan', url: (address: string) => `https://sepolia.etherscan.io/exportData?type=tokenholders&contract=${address}&decimal=18` },

  // Cronos
  '25': { name: 'CronoScan', url: (address: string) => `https://cronoscan.com/exportData?type=tokenholders&contract=${address}&decimal=18` },
  '388': { name: 'Cronos zkEVM Explorer', url: (address: string) => `https://explorer.cronos.org/zkevm/token/${address}/token-holders` },

  // Layer 2s
  '42161': { name: 'Arbiscan', url: (address: string) => `https://arbiscan.io/exportData?type=tokenholders&contract=${address}&decimal=18` },
  '10': { name: 'Optimistic Etherscan', url: (address: string) => `https://optimistic.etherscan.io/exportData?type=tokenholders&contract=${address}&decimal=18` },
  '137': { name: 'PolygonScan', url: (address: string) => `https://polygonscan.com/exportData?type=tokenholders&contract=${address}&decimal=18` },
  '8453': { name: 'BaseScan', url: (address: string) => `https://basescan.org/exportData?type=tokenholders&contract=${address}&decimal=18` },

  // Other EVM chains
  '56': { name: 'BscScan', url: (address: string) => `https://bscscan.com/exportData?type=tokenholders&contract=${address}&decimal=18` },
  '43114': { name: 'SnowTrace', url: (address: string) => `https://snowtrace.io/exportData?type=tokenholders&contract=${address}&decimal=18` },
  '250': { name: 'FTMScan', url: (address: string) => `https://ftmscan.com/exportData?type=tokenholders&contract=${address}&decimal=18` },

  // Solana
  'sol1': { name: 'Solscan', url: (address: string) => `https://solscan.io/token/${address}#holders` },
  '5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp': { name: 'Solscan Mainnet', url: (address: string) => `https://solscan.io/token/${address}#holders` }
};

// Helper function to get chain-specific explorer link
const getExplorerLink = (chainId: string, contractAddress: string) => {
  const config = EXPLORER_CONFIGS[chainId as keyof typeof EXPLORER_CONFIGS];
  if (!config) {
    return <span className="text-muted-foreground">the appropriate blockchain explorer for your chain</span>;
  }

  const url = contractAddress ? config.url(contractAddress) : '#';
  return (
    <a
      href={url}
      target="_blank"
      rel="noopener noreferrer"
      className="text-blue-600 hover:underline"
    >
      {config.name}
    </a>
  );
};

// Helper function to get chain-specific address explorer link
const getAddressExplorerUrl = (chainId: string, address: string) => {
  const baseUrls = {
    // Ethereum and testnets
    '1': 'https://etherscan.io/address',
    '11155111': 'https://sepolia.etherscan.io/address',

    // Cronos
    '25': 'https://cronoscan.com/address',
    '388': 'https://explorer.cronos.org/zkevm/address',

    // Layer 2s
    '42161': 'https://arbiscan.io/address',
    '10': 'https://optimistic.etherscan.io/address',
    '137': 'https://polygonscan.com/address',
    '8453': 'https://basescan.org/address',

    // Other EVM chains
    '56': 'https://bscscan.com/address',
    '43114': 'https://snowtrace.io/address',
    '250': 'https://ftmscan.com/address',

    // Solana
    'sol1': 'https://solscan.io/account',
    '5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp': 'https://solscan.io/account'
  };

  const baseUrl = baseUrls[chainId as keyof typeof baseUrls];
  return baseUrl ? `${baseUrl}/${address}` : `https://etherscan.io/address/${address}`;
};

export default function TokenHolderTool({ selectedChainId }: TokenHolderToolProps) {
  const [contractAddress, setContractAddress] = useState('');
  const [snapshot, setSnapshot] = useState<TokenSnapshot | null>(null);
  const [holders, setHolders] = useState<TokenHolder[]>([]);
  const [showHolders, setShowHolders] = useState(false);
  const [uploadFile, setUploadFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);



  const loadSnapshotData = async (address: string) => {
    try {
      const response = await fetch(
        `/api/admin/token-holders/${address}?chainId=${selectedChainId}&includeHolders=true`
      );

      if (!response.ok) {
        throw new Error('Failed to load snapshot data');
      }

      const data = await response.json();
      setSnapshot(data.snapshot);
      setHolders(data.holders || []);
      setShowHolders(true);
    } catch (error) {
      console.error('Error loading snapshot data:', error);
      toast.error('Failed to load snapshot data');
    }
  };

  // Load existing snapshot data when contract address is entered
  const loadExistingSnapshot = async () => {
    if (!contractAddress.trim()) {
      return;
    }

    if (!selectedChainId || selectedChainId === 'all') {
      return;
    }

    try {
      await loadSnapshotData(contractAddress.trim());
    } catch (error) {
      // Silently fail - this is just to check if data already exists
      console.log('No existing snapshot found for this contract address');
    }
  };

  const uploadCSV = async () => {
    if (!uploadFile || !contractAddress.trim()) {
      toast.error('Please select a CSV file and enter a contract address');
      return;
    }

    if (!selectedChainId || selectedChainId === 'all') {
      toast.error('Please select a specific chain');
      return;
    }

    setIsUploading(true);
    try {
      const formData = new FormData();
      formData.append('csvFile', uploadFile);
      formData.append('contractAddress', contractAddress.trim());
      formData.append('chainId', selectedChainId);
      formData.append('tokenName', ''); // Empty token name, will be auto-detected or left blank

      const response = await fetch('/api/admin/token-holders/upload', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.details || data.error || 'Failed to upload CSV');
      }

      toast.success(`Successfully processed ${data.totalHolders} token holders from CSV`);

      // Clear upload file and fetch the snapshot data to display
      setUploadFile(null);
      await loadSnapshotData(contractAddress.trim());

    } catch (error) {
      console.error('Error uploading CSV:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to upload CSV');
    } finally {
      setIsUploading(false);
    }
  };

  const downloadCSV = async () => {
    if (!contractAddress.trim() || !snapshot) {
      toast.error('No data to download');
      return;
    }

    try {
      const response = await fetch(`/api/admin/token-holders/${contractAddress.trim()}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chainId: selectedChainId,
          snapshotId: snapshot.id,
          format: 'csv',
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to download CSV');
      }

      // Create download link
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `token_holders_${contractAddress}_${snapshot.snapshotDate}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success('CSV file downloaded successfully');
    } catch (error) {
      console.error('Error downloading CSV:', error);
      toast.error('Failed to download CSV');
    }
  };

  const formatBalance = (balance: string) => {
    // Add commas to large numbers for better readability
    const num = parseFloat(balance.replace(/,/g, ''));
    return num.toLocaleString();
  };

  const formatAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Token Holder Manager
          </CardTitle>
          <CardDescription>
            Upload CSV files containing token holder data from blockchain explorers. Supports all EVM chains (0x addresses) and Solana (base58 addresses).
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="contractAddress">Contract Address *</Label>
              <Input
                id="contractAddress"
                placeholder="0x... or Solana token address"
                value={contractAddress}
                onChange={(e) => setContractAddress(e.target.value)}
              />
            </div>

            {/* Download CSV Link */}
            {contractAddress.trim() && selectedChainId && selectedChainId !== 'all' && (
              <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Download className="h-4 w-4 text-blue-600" />
                  <span className="font-medium text-blue-800 dark:text-blue-200">Download Token Holders CSV</span>
                </div>
                <p className="text-sm text-blue-700 dark:text-blue-300 mb-3">
                  Click the link below to download the token holders CSV file from the blockchain explorer:
                </p>
                <div className="flex items-center gap-2">
                  {getExplorerLink(selectedChainId, contractAddress.trim())}
                  <ExternalLink className="h-3 w-3 text-blue-600" />
                </div>
                <p className="text-xs text-blue-600 dark:text-blue-400 mt-2">
                  After downloading, upload the CSV file using the form below.
                </p>
              </div>
            )}
          </div>
          
          {selectedChainId && selectedChainId !== 'all' && (
            <div className="text-sm text-muted-foreground">
              Selected Chain: <span className="font-medium">{selectedChainId}</span>
            </div>
          )}

          {/* CSV Upload Section */}
          <div className="border-t pt-4">
            <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
              <Upload className="h-4 w-4" />
              Upload CSV File
            </h4>
            <div className="space-y-3">
              <div className="text-sm text-muted-foreground">
                Select the CSV file you downloaded from the blockchain explorer above.
              </div>
              <div className="flex items-center gap-2">
                <Input
                  type="file"
                  accept=".csv"
                  onChange={(e) => setUploadFile(e.target.files?.[0] || null)}
                  disabled={isUploading}
                  className="flex-1"
                />
                <Button
                  onClick={uploadCSV}
                  disabled={isUploading || !uploadFile || !contractAddress.trim()}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  {isUploading ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Uploading...
                    </>
                  ) : (
                    <>
                      <FileText className="h-4 w-4" />
                      Upload CSV
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex gap-2">
          <Button
            onClick={loadExistingSnapshot}
            disabled={!contractAddress.trim() || selectedChainId === 'all'}
            variant="outline"
            className="flex items-center gap-2"
          >
            <Users className="h-4 w-4" />
            Load Existing Data
          </Button>

          {snapshot && (
            <Button
              variant="outline"
              onClick={downloadCSV}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Download CSV
            </Button>
          )}
        </CardFooter>
      </Card>

      {snapshot && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Latest Snapshot
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div className="text-center p-4 bg-muted rounded-lg">
                <div className="text-2xl font-bold">{snapshot.totalHolders}</div>
                <div className="text-sm text-muted-foreground">Total Holders</div>
              </div>
              <div className="text-center p-4 bg-muted rounded-lg">
                <div className="text-lg font-semibold">{snapshot.tokenName || 'Unknown'}</div>
                <div className="text-sm text-muted-foreground">Token Name</div>
              </div>
              <div className="text-center p-4 bg-muted rounded-lg">
                <div className="text-lg font-semibold">{snapshot.snapshotDate}</div>
                <div className="text-sm text-muted-foreground">Snapshot Date</div>
              </div>
            </div>

            {showHolders && holders.length > 0 && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-lg font-semibold">Top Holders</h4>
                  <div className="text-sm text-muted-foreground">
                    Showing top {Math.min(10, holders.length)} of {holders.length} holders
                  </div>
                </div>
                
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Rank</TableHead>
                        <TableHead>Address</TableHead>
                        <TableHead className="text-right">Balance</TableHead>
                        <TableHead className="text-center">Pending Update</TableHead>
                        <TableHead className="text-center">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {holders.slice(0, 10).map((holder, index) => (
                        <TableRow key={holder.holderAddress}>
                          <TableCell className="font-medium">#{index + 1}</TableCell>
                          <TableCell className="font-mono">
                            {formatAddress(holder.holderAddress)}
                          </TableCell>
                          <TableCell className="text-right font-mono">
                            {formatBalance(holder.balance)}
                          </TableCell>
                          <TableCell className="text-center">
                            <span className={`px-2 py-1 rounded-full text-xs ${
                              holder.pendingBalanceUpdate === 'Yes'
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-green-100 text-green-800'
                            }`}>
                              {holder.pendingBalanceUpdate || 'No'}
                            </span>
                          </TableCell>
                          <TableCell className="text-center">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => window.open(getAddressExplorerUrl(selectedChainId, holder.holderAddress), '_blank')}
                            >
                              <ExternalLink className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
