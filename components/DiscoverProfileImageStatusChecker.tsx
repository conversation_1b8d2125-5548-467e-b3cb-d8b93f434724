'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAppKitAccount } from '@reown/appkit/react';

interface ProfileStatusCheckerProps {
  profileAddress: string;
  profileName?: string;
  children: React.ReactNode;
}

// This component is specifically for the image part of profile cards
// It shows the status text overlay
export default function DiscoverProfileImageStatusChecker({
  profileAddress,
  profileName,
  children
}: ProfileStatusCheckerProps) {
  const router = useRouter();
  const { address } = useAppKitAccount();
  const [status, setStatus] = useState<string | null>(null);
  const [isChecking, setIsChecking] = useState(true);
  const [isApproved, setIsApproved] = useState(false);
  const [isOwnProfile, setIsOwnProfile] = useState(false);

  useEffect(() => {
    const checkProfileStatus = async () => {
      try {
        setIsChecking(true);

        // Use the profile name if available, otherwise use the address
        const identifier = profileName || profileAddress;
        console.log('[DiscoverProfileImageStatusChecker] Checking profile:', identifier);

        // Fetch profile status
        const response = await fetch(`/api/profile/check-status?identifier=${identifier}`);
        const data = await response.json();
        console.log('[DiscoverProfileImageStatusChecker] Profile status data:', data);

        // Check if this is the user's own profile
        const isOwn = address && data.address &&
          address.toLowerCase() === data.address.toLowerCase();
        console.log('[DiscoverProfileImageStatusChecker] Is own profile:', isOwn);

        setStatus(data.status);
        setIsApproved(data.status === 'approved');
        setIsOwnProfile(isOwn);
      } catch (error) {
        console.error('Error checking profile status:', error);
      } finally {
        setIsChecking(false);
      }
    };

    if (profileAddress) {
      checkProfileStatus();
    }
  }, [profileAddress, profileName, address]);

  const handleClick = (e: React.MouseEvent) => {
    // If profile is not approved, prevent navigation
    // For the Discover page, we don't make an exception for the owner's profile
    if (!isApproved && status !== 'not-found') {
      console.log('[DiscoverProfileImageStatusChecker] Preventing navigation for status:', status);
      e.preventDefault();
      e.stopPropagation();
      // Use window.location for a hard redirect instead of router.push
      // Use the name parameter instead of address
      const nameParam = profileName ? `&name=${profileName}` : '';
      window.location.href = `/profile-error?status=${status}${nameParam}`;
    } else {
      console.log('[DiscoverProfileImageStatusChecker] Allowing navigation for status:', status);
    }
  };

  if (isChecking) {
    return <>{children}</>;
  }

  // If profile is approved, render normally
  // We don't make an exception for the owner's profile in the Discover page
  if (isApproved || status === 'not-found') {
    return <>{children}</>;
  }

  // For non-approved profiles, add a visual indicator below the image
  return (
    <div onClick={handleClick} className="cursor-pointer relative">
      {children}
      <div className="absolute bottom-0 left-0 right-0 bg-black/70 py-1 px-2 text-center z-20">
        <div className="text-xs text-white">
          {status === 'new' && "Action Needed"}
          {status === 'expired' && "Profile Expired"}
          {status === 'in-progress' && "In Progress"}
          {status === 'pending' && "Pending Approval"}
          {status === 'deleted' && "Deleted"}
        </div>
      </div>
    </div>
  );
}
