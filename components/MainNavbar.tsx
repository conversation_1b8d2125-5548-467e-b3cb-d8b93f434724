"use client"

import * as React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import App<PERSON><PERSON><PERSON>utt<PERSON> from "./AppKitButton"

import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu"

export default function MainNavbar() {
  const pathname = usePathname()

  return (
    <header className="fixed top-0 left-0 right-0 z-50 border-b border-transparent bg-transparent backdrop-blur-md">
      <div className="container flex h-16 items-center justify-between">
        <NavigationMenu className="flex-1">

          <NavigationMenuList className="flex flex-wrap gap-1 sm:gap-2">
            <NavigationMenuItem>
              <NavigationMenuLink
                asChild
                className={navigationMenuTriggerStyle() + " text-sm sm:text-base px-2 sm:px-4"}
                data-active={pathname === "/"}
              >
                <Link href="/">Home</Link>
              </NavigationMenuLink>
            </NavigationMenuItem>

            <NavigationMenuItem>
              <NavigationMenuLink
                asChild
                className={navigationMenuTriggerStyle() + " text-sm sm:text-base px-2 sm:px-4"}
                data-active={pathname === "/createpage"}
              >
                <Link href="/createpage">Create Page</Link>
              </NavigationMenuLink>
            </NavigationMenuItem>

            <NavigationMenuItem>
              <NavigationMenuLink
                asChild
                className={navigationMenuTriggerStyle() + " text-sm sm:text-base px-2 sm:px-4"}
                data-active={pathname === "/pagelayout"}
              >
                <Link href="/pagelayout">Page Layout</Link>
              </NavigationMenuLink>
            </NavigationMenuItem>

            <NavigationMenuItem>
              <NavigationMenuLink
                asChild
                className={navigationMenuTriggerStyle() + " text-sm sm:text-base px-2 sm:px-4"}
                data-active={pathname === "/view"}
              >
                <Link href="/view">View Page</Link>
              </NavigationMenuLink>
            </NavigationMenuItem>
          </NavigationMenuList>
        </NavigationMenu>
        <div className="ml-4">
          <AppKitButton />
        </div>
      </div>
    </header>
  )
}



