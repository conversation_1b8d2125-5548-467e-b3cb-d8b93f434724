'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAppKitAccount } from '@reown/appkit/react';

interface ProfileStatusCheckerProps {
  profileAddress: string;
  profileName?: string;
  children: React.ReactNode;
}

export default function DiscoverProfileStatusChecker({
  profileAddress,
  profileName,
  children
}: ProfileStatusCheckerProps) {
  const router = useRouter();
  const { address } = useAppKitAccount();
  const [status, setStatus] = useState<string | null>(null);
  const [isChecking, setIsChecking] = useState(true);
  const [isApproved, setIsApproved] = useState(false);
  const [isOwnProfile, setIsOwnProfile] = useState(false);

  useEffect(() => {
    const checkProfileStatus = async () => {
      try {
        setIsChecking(true);

        // Use the profile name if available, otherwise use the address
        const identifier = profileName || profileAddress;
        console.log('[DiscoverProfileStatusChecker] Checking profile:', identifier);

        // Fetch profile status
        const response = await fetch(`/api/profile/check-status?identifier=${identifier}`);
        const data = await response.json();
        console.log('[DiscoverProfileStatusChecker] Profile status data:', data);

        // Check if this is the user's own profile
        const isOwn = address && data.address &&
          address.toLowerCase() === data.address.toLowerCase();
        console.log('[DiscoverProfileStatusChecker] Is own profile:', isOwn);

        setStatus(data.status);
        setIsApproved(data.status === 'approved');
        setIsOwnProfile(isOwn);
      } catch (error) {
        console.error('Error checking profile status:', error);
      } finally {
        setIsChecking(false);
      }
    };

    if (profileAddress) {
      checkProfileStatus();
    }
  }, [profileAddress, profileName, address]);

  const handleClick = (e: React.MouseEvent) => {
    // If profile is not approved and it's not the user's own profile, prevent navigation
    if (!isApproved && !isOwnProfile && status !== 'not-found') {
      console.log('[DiscoverProfileStatusChecker] Preventing navigation for status:', status);
      e.preventDefault();
      e.stopPropagation();
      // Use window.location for a hard redirect instead of router.push
      window.location.href = `/profile-error?status=${status}`;
    } else {
      console.log('[DiscoverProfileStatusChecker] Allowing navigation for status:', status);
    }
  };

  if (isChecking) {
    return <>{children}</>;
  }

  // If profile is approved or it's the user's own profile, render normally
  if (isApproved || isOwnProfile || status === 'not-found') {
    return <>{children}</>;
  }

  // For non-approved profiles that aren't the user's own, add a visual indicator
  return (
    <div onClick={handleClick} className="cursor-pointer relative">
      {children}
      <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
        <div className="bg-black/80 p-2 rounded text-xs text-center">
          {status === 'new' && "Action Needed"}
          {status === 'in-progress' && "In Progress"}
          {status === 'pending' && "Pending Approval"}
          {status === 'deleted' && "Deleted"}
          {status === 'expired' && "Expired"}
        </div>
      </div>
    </div>
  );
}
