"use client";
import {
  Navbar,
  NavBody,
} from "@/components/ui/resizable-navbar";
import React from "react";
import { usePathname } from "next/navigation";
import Image from "next/image";
import CustomConnectButton from "./CustomConnectButton";

export default function SocialNavbar() {
  const pathname = usePathname();

  // Navigation items with simplified labels
  const navItems = [
    {
      name: "Home",
      link: "/",
    },
    {
      name: "Create",
      link: "/createpage",
    },
    {
      name: "Layout",
      link: "/layoutpage",
    },
    {
      name: "View",
      link: "/view",
    },
    {
      name: "Discover",
      link: "/discover",
    },
  ];



  // Custom styles for the navigation items
  const customNavItemsStyle = `
    .social-navbar-items {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      padding: 0;
      margin: 0 auto;
    }
    .social-navbar-items a {
      padding: 0.25rem 0.375rem;
      margin: 0 0.125rem;
      font-size: 0.75rem;
      font-weight: 500;
      border-radius: 9999px;
      transition: all 0.2s;
      display: inline-block;
      text-align: center;
    }
    .social-navbar-items a:hover {
      background-color: rgba(59, 130, 246, 0.2);
    }
    .social-navbar-items a[data-active="true"] {
      background-color: rgba(59, 130, 246, 0.3);
      color: white;
    }
    @media (min-width: 640px) {
      .social-navbar-items a {
        padding: 0.375rem 0.75rem;
        margin: 0 0.375rem;
      }
    }
  `;

  // Add additional CSS for sticky behavior
  const stickyNavbarStyle = `
    /* Override the default navbar styles to make it sticky at the top */
    .navbar-override {
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      z-index: 9999 !important;
    }
  `;

  return (
      <div>
        {/* Add custom styles */}
        <style dangerouslySetInnerHTML={{ __html: customNavItemsStyle + stickyNavbarStyle }} />
        <Navbar className="transition-all duration-200 navbar-override">
          {/* Only use NavBody for all screen sizes */}
          <NavBody className="!flex !w-full !min-w-0 !max-w-full pl-2 pr-0 py-2 relative transition-all duration-200 bg-black/80 backdrop-blur-lg border-b border-neutral-800/50 flex-col">
            {/* Row with logo, navigation, and wallet button */}
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center gap-1 flex-shrink-0">
                <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center">
                  <Image
                    src={`/w3t.gif?v=${Date.now()}`}
                    alt="Web3 Socials"
                    width={24}
                    height={24}
                    className="rounded-full"
                    priority
                  />
                </div>
                <span className="hidden sm:inline-block text-sm md:text-lg font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-600">
                  Web3 Socials
                </span>
              </div>

              {/* Navigation items - centered on desktop, shifted left on mobile */}
              <div className="absolute inset-y-0 left-8 right-16 sm:left-0 sm:right-0 flex items-center justify-start sm:justify-center">
                <div className="social-navbar-items">
                  {navItems.map((item, idx) => (
                    <a
                      key={`nav-link-${idx}`}
                      href={item.link}
                      data-active={pathname === item.link}
                      className="relative px-4 py-2 text-neutral-300 hover:text-white"
                    >
                      <span className="relative z-20">{item.name}</span>
                    </a>
                  ))}
                </div>
              </div>

              {/* Wallet connect button - compact on mobile, normal spacing on desktop */}
              <div className="flex items-center flex-shrink-0 mr-1 sm:mr-2">
                <div className="social-navbar-items">
                  <CustomConnectButton />
                </div>
              </div>
            </div>
          </NavBody>
        </Navbar>
      </div>
  );
}
