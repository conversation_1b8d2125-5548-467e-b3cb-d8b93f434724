import { FC, CSSProperties } from "react";
import "./GlitchText.css";

interface GlitchTextProps {
    children: string;
    speed?: number;
    enableShadows?: boolean;
    enableOnHover?: boolean;
    className?: string;
}

interface CustomCSSProperties extends CSSProperties {
    "--after-duration": string;
    "--before-duration": string;
    "--after-shadow": string;
    "--before-shadow": string;
}

const GlitchText: FC<GlitchTextProps> = ({
                                             children,
                                             speed = 0.5,
                                             enableShadows = true,
                                             enableOnHover = false,
                                             className = "",
                                         }) => {
    const inlineStyles: CustomCSSProperties = {
        "--after-duration": `${speed * 3}s`,
        "--before-duration": `${speed * 2}s`,
        "--after-shadow": enableShadows ? "-5px 0 rgba(255, 0, 0, 0.7)" : "none",
        "--before-shadow": enableShadows ? "5px 0 rgba(0, 255, 255, 0.7)" : "none",
    };

    const hoverClass = enableOnHover ? "enable-on-hover" : "";

    return (
        <div
            className={`glitch ${hoverClass} ${className}`}
            style={{
                ...inlineStyles,
                display: 'inline-block',
                textAlign: 'center',
                width: 'auto'
            }}
            data-text={children}
        >
            {children}
        </div>
    );
};

export default GlitchText;
