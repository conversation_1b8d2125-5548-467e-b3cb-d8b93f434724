import React, { useRef, useLayoutEffect, useState } from "react";
import {
    motion,
    useScroll,
    useSpring,
    useTransform,
    useMotionValue,
    useVelocity,
    useAnimationFrame,
} from "motion/react";

interface VelocityMapping {
    input: [number, number];
    output: [number, number];
}

interface VelocityTextProps {
    children: React.ReactNode;
    baseVelocity: number;
    scrollContainerRef?: React.RefObject<HTMLElement>;
    className?: string;
    damping?: number;
    stiffness?: number;
    numCopies?: number;
    velocityMapping?: VelocityMapping;
    parallaxClassName?: string;
    scrollerClassName?: string;
    parallaxStyle?: React.CSSProperties;
    scrollerStyle?: React.CSSProperties;
}

interface ScrollVelocityProps {
    scrollContainerRef?: React.RefObject<HTMLElement>;
    texts: string[];
    velocity?: number;
    className?: string;
    damping?: number;
    stiffness?: number;
    numCopies?: number;
    velocityMapping?: VelocityMapping;
    parallaxClassName?: string;
    scrollerClassName?: string;
    parallaxStyle?: React.CSSProperties;
    scrollerStyle?: React.CSSProperties;
}

function useElementWidth(ref: React.RefObject<HTMLElement>): number {
    const [width, setWidth] = useState(0);

    useLayoutEffect(() => {
        function updateWidth() {
            if (ref.current) {
                setWidth(ref.current.offsetWidth);
            }
        }
        updateWidth();
        window.addEventListener("resize", updateWidth);
        return () => window.removeEventListener("resize", updateWidth);
    }, [ref]);

    return width;
}

export const ScrollVelocity: React.FC<ScrollVelocityProps> = ({
                                                                  scrollContainerRef,
                                                                  texts = [],
                                                                  velocity = 100,
                                                                  className = "",
                                                                  damping = 50,
                                                                  stiffness = 400,
                                                                  numCopies = 6,
                                                                  velocityMapping = { input: [0, 1000], output: [0, 5] },
                                                                  parallaxClassName = "parallax",
                                                                  scrollerClassName = "scroller",
                                                                  parallaxStyle,
                                                                  scrollerStyle,
                                                              }) => {
    function VelocityText({
                              children,
                              baseVelocity = velocity,
                              scrollContainerRef,
                              className = "",
                              damping,
                              stiffness,
                              numCopies,
                              velocityMapping,
                              parallaxClassName,
                              scrollerClassName,
                              parallaxStyle,
                              scrollerStyle,
                          }: VelocityTextProps) {
        const baseX = useMotionValue(0);
        const scrollOptions = scrollContainerRef ? { container: scrollContainerRef } : {};
        const { scrollY } = useScroll(scrollOptions);
        const scrollVelocity = useVelocity(scrollY);
        const smoothVelocity = useSpring(scrollVelocity, {
            damping: damping ?? 50,
            stiffness: stiffness ?? 400,
        });
        const velocityFactor = useTransform(
            smoothVelocity,
            velocityMapping?.input || [0, 1000],
            velocityMapping?.output || [0, 5],
            { clamp: false }
        );

        const copyRef = useRef<HTMLElement>(null);
        const copyWidth = useElementWidth(copyRef as React.RefObject<HTMLElement>);

        // Completely redesigned wrap function for continuous scrolling
        function wrap(min: number, max: number, v: number): number {
            const range = max - min;
            // Simple modulo without any jumps
            return min + ((v - min) % range);
        }

        const x = useTransform(baseX, (v) => {
            if (copyWidth === 0) return "0px";
            // Create a continuous scrolling effect
            return `${wrap(-copyWidth, 0, v)}px`;
        });

        const directionFactor = useRef<number>(1);
        useAnimationFrame((t, delta) => {
            // Calculate base movement with consistent speed
            let moveBy = directionFactor.current * baseVelocity * (delta / 1000);

            // Set direction based on velocity factor
            if (velocityFactor.get() < 0) {
                directionFactor.current = -1;
            } else if (velocityFactor.get() > 0) {
                directionFactor.current = 1;
            }

            // Apply velocity factor with a damping effect for smoother animation
            const velocityInfluence = directionFactor.current * moveBy * velocityFactor.get() * 0.8;
            moveBy += velocityInfluence;

            // Update position smoothly
            baseX.set(baseX.get() + moveBy);
        });

        const spans = [];
        for (let i = 0; i < numCopies!; i++) {
            spans.push(
                <span className={className} key={i} ref={i === 0 ? copyRef : null}>
          {children}
        </span>
            );
        }

        return (
            <div className={parallaxClassName} style={parallaxStyle}>
                <motion.div
                    className={scrollerClassName}
                    style={{ x, ...scrollerStyle }}
                >
                    {spans}
                </motion.div>
            </div>
        );
    }

    return (
        <section>
            {texts.map((text: string, index: number) => (
                <VelocityText
                    key={index}
                    className={className}
                    baseVelocity={index % 2 !== 0 ? -velocity : velocity}
                    scrollContainerRef={scrollContainerRef}
                    damping={damping}
                    stiffness={stiffness}
                    numCopies={numCopies}
                    velocityMapping={velocityMapping}
                    parallaxClassName={parallaxClassName}
                    scrollerClassName={scrollerClassName}
                    parallaxStyle={parallaxStyle}
                    scrollerStyle={scrollerStyle}
                >
                    {text}&nbsp;
                </VelocityText>
            ))}
        </section>
    );
};

export default ScrollVelocity;
