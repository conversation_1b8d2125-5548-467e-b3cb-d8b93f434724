'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface FloatingDockProps {
  items: {
    title: string;
    icon: React.ReactNode;
    href: string;
  }[];
  className?: string;
}

export const FloatingDock = ({ items, className }: FloatingDockProps) => {
  return (
    <div className={cn("flex flex-wrap justify-center gap-4 py-2", className)}>
      {items.map((item) => (
        <div className="flex flex-col items-center" key={item.title}>
          <a
            href={item.href}
            target="_blank"
            rel="noopener noreferrer"
            className="flex h-10 w-10 sm:h-12 sm:w-12 items-center justify-center rounded-full overflow-hidden bg-neutral-900 p-0 relative hover:scale-110 transition-transform duration-200"
          >
            <div className="h-full w-full">{item.icon}</div>
          </a>
          <span className="text-xs text-white mt-2 block text-center">{item.title}</span>
        </div>
      ))}
    </div>
  );
};
