"use client";
import React, { useEffect, useRef, useState } from "react";
import { useMotionValueEvent, useScroll } from "motion/react";
import { motion } from "motion/react";
import { cn } from "@/lib/utils";
import "@/app/components/ui/scrollbar.css";
import "@/app/components/ui/smallest-screen.css";

export const StickyScroll = ({
                                 content,
                                 contentClassName,
                                 fontColor,
                             }: {
    content: {
        title: string;
        description: string | React.ReactNode;
        content?: React.ReactNode | any;
        customBackground?: string | null;
        customScrollContainerClass?: string;
    }[];
    contentClassName?: string;
    fontColor?: string | null;
}) => {
    const [activeCard, setActiveCard] = React.useState(0);
    const ref = useRef<any>(null);
    const { scrollYProgress } = useScroll({
        // uncomment line 22 and comment line 23 if you DONT want the overflow container and want to have it change on the entire page scroll
        // target: ref
        container: ref,
        offset: ["start start", "end end"],
        // initial value is no longer supported in the useScroll options
    });
    const cardLength = content.length;

    // Add a ref to track the section elements
    const sectionRefs = useRef<Array<HTMLDivElement | null>>([]);

    // Update refs when content changes and ensure first section is active
    useEffect(() => {
        sectionRefs.current = sectionRefs.current.slice(0, content.length);
        // Force the first section to be active initially
        setActiveCard(0);
    }, [content]);

    // Calculate which section is in the center of the viewport
    useMotionValueEvent(scrollYProgress, "change", (latest) => {
        // If no refs yet, use simple calculation
        if (sectionRefs.current.length === 0 || !sectionRefs.current[0]) {
            const clampedLatest = Math.max(0, Math.min(latest, 0.999));
            const cardIndex = Math.min(Math.floor(clampedLatest * cardLength), cardLength - 1);
            if (cardIndex !== activeCard) {
                setActiveCard(cardIndex);
            }
            return;
        }

        // Get the container dimensions
        const container = ref.current;
        if (!container) return;

        // Calculate the center point of the container
        const containerCenter = container.scrollTop + container.clientHeight / 2;

        // Find which section is closest to the center
        let closestSectionIndex = 0;
        let closestDistance = Infinity;

        sectionRefs.current.forEach((sectionRef, index) => {
            if (!sectionRef) return;

            // Get the section's position relative to the container
            const sectionTop = sectionRef.offsetTop;
            const sectionCenter = sectionTop + sectionRef.clientHeight / 2;

            // Calculate distance from section center to container center
            const distance = Math.abs(sectionCenter - containerCenter);

            // Update closest section if this one is closer
            if (distance < closestDistance) {
                closestDistance = distance;
                closestSectionIndex = index;
            }
        });

        // Update active card if it changed
        if (closestSectionIndex !== activeCard) {
            setActiveCard(closestSectionIndex);
        }
    });

    const backgroundColors = [
        "transparent", // transparent background
        "transparent", // transparent background
        "transparent", // transparent background
    ];
    const linearGradients = [
        "linear-gradient(to bottom right, #06b6d4, #10b981)", // cyan-500 to emerald-500
        "linear-gradient(to bottom right, #ec4899, #6366f1)", // pink-500 to indigo-500
        "linear-gradient(to bottom right, #f97316, #eab308)", // orange-500 to yellow-500
    ];

    const [backgroundGradient, setBackgroundGradient] = useState(
        linearGradients[0],
    );

    // State for responsive styling
    const [rightPosition, setRightPosition] = useState('10%');
    const [imageHeight, setImageHeight] = useState('15rem');
    const [maxWidth, setMaxWidth] = useState('20rem');

    useEffect(() => {
        if (activeCard >= 0 && activeCard < linearGradients.length) {
            setBackgroundGradient(linearGradients[activeCard % linearGradients.length]);
            // Setting gradient for active card
        }
    }, [activeCard]);

    // Check if there's only one section
    const isSingleSection = content.length === 1;

    // For single section, use a completely different layout
    if (isSingleSection) {
        return (
            <motion.div
                animate={{}}
                className="relative flex flex-row h-[20rem] sm:h-[26rem] md:h-[28rem] justify-center space-x-0 sm:space-x-2 md:space-x-4 py-0 sm:py-6 md:py-10 px-0 sm:px-1 md:px-3 scroll-smooth w-full m-0 xs:mr-2 overflow-x-hidden overflow-y-scroll custom-scrollbar"
                style={{}}
                ref={ref}
            >
                <div className="div relative flex items-start px-2 sm:px-3 md:px-4 pr-4 sm:pr-4 md:pr-5 w-full m-0 overflow-visible text-container">
                    <div className="w-full max-w-[12rem] sm:max-w-[16rem] md:max-w-xs overflow-visible">
                        {/* Add negative margin to match multiple section layout */}
                        <div className="-mt-0 sm:-mt-12"></div>

                        {/* Add minimal space at the top */}
                        <div className="h-[3rem] sm:h-[4rem]" />

                        <div className="my-24 sm:my-24 md:my-32">
                            <motion.h2
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                className="text-lg sm:text-xl md:text-2xl font-bold"
                                style={{ color: fontColor || '#ffffff' }}
                            >
                                {content[0].title}
                            </motion.h2>
                            <motion.p
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                className="text-xs sm:text-sm md:text-base mt-3 sm:mt-4 md:mt-6 max-w-full sm:max-w-[12rem] md:max-w-xs line-clamp-4 sm:line-clamp-5 md:line-clamp-6"
                                style={{ color: fontColor || '#ffffff' }}
                            >
                                {content[0].description}
                            </motion.p>
                        </div>

                        {/* Add space after the section */}
                        <div className="h-[2rem] sm:h-[8rem]" />
                    </div>
                </div>

                <div className="flex items-center justify-center sticky top-0 h-full overflow-visible pr-0 container-wrapper">
                    <div
                        style={{
                            background: content[0]?.customBackground || backgroundGradient
                        }}
                        className={cn(
                            "h-52 w-52 sm:h-64 sm:w-64 md:w-72 md:h-72 overflow-hidden bg-transparent xs:mr-2 content-container",
                            contentClassName,
                        )}
                    >
                        {content[0] && content[0].content ? content[0].content : null}
                    </div>
                </div>
            </motion.div>
        );
    }

    // Original layout for multiple sections
    return (
        <motion.div
            animate={{}}
            className="relative flex flex-row h-[20rem] sm:h-[26rem] md:h-[28rem] justify-center space-x-0 sm:space-x-2 md:space-x-4 overflow-x-hidden overflow-y-scroll custom-scrollbar py-0 sm:py-6 md:py-10 px-0 sm:px-1 md:px-3 scroll-smooth w-full m-0 xs:mr-2"
            style={{
                scrollPaddingTop: '0px',
                scrollPaddingBottom: '0px',
                scrollSnapType: 'none',
                scrollBehavior: 'smooth'
            }}
            ref={ref}
        >
            <div className="div relative flex items-start px-2 sm:px-3 md:px-4 pr-4 sm:pr-4 md:pr-5 w-full m-0 overflow-visible text-container">
                <div className="w-full max-w-[12rem] sm:max-w-[16rem] md:max-w-xs overflow-visible">
                    {/* Add negative margin to the first section */}
                    <div className="-mt-0 sm:-mt-12"></div>

                    {/* Add minimal space at the top */}
                    <div className="h-[3rem] sm:h-[4rem]" />

                    {content.map((item, index) => (
                        <div
                            key={item.title + index}
                            className="my-24 sm:my-24 md:my-32"
                            ref={(el: HTMLDivElement | null) => {
                                if (sectionRefs.current) {
                                    sectionRefs.current[index] = el;
                                }
                            }}
                        >
                            <motion.h2
                                initial={{
                                    opacity: 0,
                                }}
                                animate={{
                                    opacity: activeCard === index ? 1 : 0.3,
                                }}
                                className="text-lg sm:text-xl md:text-2xl font-bold"
                                style={{ color: fontColor || '#ffffff' }}
                            >
                                {item.title}
                            </motion.h2>
                            <motion.p
                                initial={{
                                    opacity: 0,
                                }}
                                animate={{
                                    opacity: activeCard === index ? 1 : 0.3,
                                }}
                                className="text-xs sm:text-sm md:text-base mt-3 sm:mt-4 md:mt-6 max-w-full sm:max-w-[12rem] md:max-w-xs line-clamp-4 sm:line-clamp-5 md:line-clamp-6"
                                style={{ color: fontColor || '#ffffff' }}
                            >
                                {item.description}
                            </motion.p>
                        </div>
                    ))}

                    {/* Add space after the last section */}
                    <div className="h-[2rem] sm:h-[8rem]" />
                </div>
            </div>
            <div className="flex items-center justify-center sticky top-0 h-full overflow-visible pr-0 container-wrapper">
                <div
                    style={{
                        background: content[activeCard]?.customBackground || backgroundGradient
                    }}
                    className={cn(
                        "h-52 w-52 sm:h-64 sm:w-64 md:w-72 md:h-72 overflow-hidden bg-transparent xs:mr-2 content-container",
                        contentClassName,
                    )}
                >
                    {content[activeCard] && content[activeCard].content ? content[activeCard].content : null}
                </div>
            </div>
        </motion.div>
    );
};
