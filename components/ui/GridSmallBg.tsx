import { cn } from "@/lib/utils";
import React from "react";

interface GridSmallBgProps {
  children?: React.ReactNode;
  className?: string;
  showRadialGradient?: boolean;
  disabled?: boolean;
}

export function GridSmallBg({
  children,
  className,
  showRadialGradient = true,
  disabled = false
}: GridSmallBgProps) {
    return (
        <div className={cn(
          "relative w-full",
          className
        )}>
            {!disabled && (
              <>
                <div
                    className={cn(
                        "absolute inset-0",
                        "[background-size:20px_20px]",
                        "[background-image:linear-gradient(to_right,#e4e4e7_1px,transparent_1px),linear-gradient(to_bottom,#e4e4e7_1px,transparent_1px)]",
                        "dark:[background-image:linear-gradient(to_right,#262626_1px,transparent_1px),linear-gradient(to_bottom,#262626_1px,transparent_1px)]",
                        "z-0"
                    )}
                />
                {/* Radial gradient for the container to give a faded look */}
                {showRadialGradient && (
                  <div className="pointer-events-none absolute inset-0 flex items-center justify-center bg-white [mask-image:radial-gradient(ellipse_at_center,transparent_20%,black)] dark:bg-black z-0"></div>
                )}
              </>
            )}
            {children}
        </div>
    );
}

// Keep the demo component for reference or testing
export function GridSmallBackgroundDemo() {
    return (
        <GridSmallBg className="flex h-[50rem] items-center justify-center bg-white dark:bg-black">
            <p className="relative z-20 bg-gradient-to-b from-neutral-200 to-neutral-500 bg-clip-text py-8 text-4xl font-bold text-transparent sm:text-7xl">
                Backgrounds
            </p>
        </GridSmallBg>
    );
}
