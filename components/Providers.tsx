'use client';

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { WagmiProvider } from "wagmi";
import { config } from "@/config";
import React, { useState, useEffect } from "react";
import { MetadataProvider } from "@/app/contexts/MetadataContext";
import { ThemeProvider } from "@/components/ThemeProvider";
import { GridBgProvider } from "@/app/contexts/GridBgContext";
import { useWalletConnectionPersistence } from "@/hooks/useWalletConnectionPersistence";
import AppKitErrorBoundary from "@/components/AppKitErrorBoundary";

// Create a client outside the component
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
    },
  },
});

// Inner component to use hooks after WagmiProvider is mounted
function ProvidersContent({ children }: { children: React.ReactNode }) {
  // Use the wallet connection persistence hook
  useWalletConnectionPersistence();

  return (
    <MetadataProvider>
      <GridBgProvider>
        {children}
      </GridBgProvider>
    </MetadataProvider>
  );
}

export default function Providers({ children }: { children: React.ReactNode }) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  return (
    <WagmiProvider config={config}>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider attribute="class" defaultTheme="dark" enableSystem={false} forcedTheme="dark">
          <ProvidersContent>
            {children}
          </ProvidersContent>
        </ThemeProvider>
      </QueryClientProvider>
    </WagmiProvider>
  );
}


