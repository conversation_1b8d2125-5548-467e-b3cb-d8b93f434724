'use server';

import { db } from '@/db/drizzle';
import { waitingList } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { getAddress } from 'viem';

/**
 * Add an X handle to the waiting list
 * @param xHandle The X handle to add
 * @returns Object with success status and message
 */
export async function addxHandle(xHandle: string) {
  try {
    // Basic validation
    if (!xHandle || xHandle.trim() === '') {
      return {
        success: false,
        message: 'Please enter a valid X handle'
      };
    }

    // Clean up the handle (remove @ if present)
    const cleanHandle = xHandle.trim().replace(/^@/, '');

    // Generate a deterministic address from the X handle
    // This is a simple hash function to create a unique address for each handle
    // In a real app, this would be the user's wallet address
    const encoder = new TextEncoder();
    const data = encoder.encode(cleanHandle);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hashHex = '0x' + hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    
    // Use the first 40 chars after 0x to create an Ethereum-like address
    const address = hashHex.substring(0, 42);
    
    // Check if this handle is already in the waiting list
    const existing = await db
      .select()
      .from(waitingList)
      .where(eq(waitingList.xHandle, cleanHandle));

    if (existing.length > 0) {
      return {
        success: false,
        message: 'This X handle is already on the waiting list'
      };
    }

    // Add to waiting list
    await db.insert(waitingList).values({
      address,
      chain: '25', // Default chain ID (Cronos)
      xHandle: cleanHandle,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    return {
      success: true,
      message: 'Successfully added to the waiting list!'
    };
  } catch (error) {
    console.error('Error adding to waiting list:', error);
    return {
      success: false,
      message: 'Failed to add to waiting list. Please try again later.'
    };
  }
}
