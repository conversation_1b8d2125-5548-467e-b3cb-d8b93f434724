#!/bin/bash

# Production Database Setup Script for Web3Socials
# For Hostinger VPS with CloudPanel and MySQL
# This script will set up a fresh database with all required tables

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "🚀 Web3Socials Production Database Setup"
print_status "========================================"

# Check if .env file exists
if [[ ! -f ".env" ]]; then
    print_error ".env file not found!"
    print_error "Please create a .env file with your database configuration:"
    echo ""
    echo "DATABASE_URL=mysql://username:password@localhost:3306/database_name"
    echo "MYSQL_SSL=false"
    echo "NEXT_PUBLIC_PROJECT_ID=your_reown_project_id"
    echo "NODE_ENV=production"
    exit 1
fi

print_status "✅ .env file found"

# Check if Node.js and npm are available
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed!"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    print_error "npm is not installed!"
    exit 1
fi

print_status "✅ Node.js and npm are available"

# Install dependencies if node_modules doesn't exist
if [[ ! -d "node_modules" ]]; then
    print_status "📦 Installing dependencies..."
    npm install
    print_success "Dependencies installed"
else
    print_status "✅ Dependencies already installed"
fi

# Backup existing database (optional)
read -p "Do you want to backup the existing database before reset? (y/N): " backup_choice
if [[ $backup_choice =~ ^[Yy]$ ]]; then
    print_status "📋 Creating database backup..."
    # Extract database info from .env
    DB_URL=$(grep "DATABASE_URL" .env | cut -d '=' -f2)
    if [[ $DB_URL =~ mysql://([^:]+):([^@]+)@([^:]+):([0-9]+)/(.+) ]]; then
        DB_USER="${BASH_REMATCH[1]}"
        DB_PASS="${BASH_REMATCH[2]}"
        DB_HOST="${BASH_REMATCH[3]}"
        DB_PORT="${BASH_REMATCH[4]}"
        DB_NAME="${BASH_REMATCH[5]}"
        
        BACKUP_FILE="backup_$(date +%Y%m%d_%H%M%S).sql"
        print_status "Creating backup: $BACKUP_FILE"
        mysqldump -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" > "$BACKUP_FILE"
        print_success "Backup created: $BACKUP_FILE"
    else
        print_warning "Could not parse DATABASE_URL for backup"
    fi
fi

# Confirm database reset
print_warning "⚠️  WARNING: This will DELETE ALL DATA in your database!"
print_warning "This action cannot be undone."
echo ""
read -p "Are you sure you want to proceed with database reset? (y/N): " confirm

if [[ ! $confirm =~ ^[Yy]$ ]]; then
    print_status "Operation cancelled by user"
    exit 0
fi

print_status "🗄️  Resetting database and creating all tables..."
npm run reset-database

if [[ $? -eq 0 ]]; then
    print_success "Database reset completed successfully!"
else
    print_error "Database reset failed!"
    exit 1
fi

print_status "🔧 Verifying chain defaults..."
npm run verify-chain-defaults

if [[ $? -eq 0 ]]; then
    print_success "Chain defaults verified!"
else
    print_warning "Chain defaults verification had issues - check output above"
fi

print_status "✅ Checking all tables..."
npm run check-tables

print_success "🎉 Production database setup completed successfully!"
print_status "========================================"
print_status "📋 Summary of created tables:"
echo "   • system_settings - System configuration"
echo "   • web3Profile - User profiles"
echo "   • waitingList - User waiting list"
echo "   • componentPositions - Component layouts"
echo "   • componentImages - Image storage"
echo "   • profileLikes - Like system"
echo "   • profileReferrals - Referral system"
echo "   • token_holder_snapshots - Token holder metadata"
echo "   • token_holders - Token holder data"
echo "   • token_holder_jobs - Token fetch jobs"
echo "   • balance_check_logs - Balance check logs"
echo ""
print_status "🚀 Your database is ready for production!"
print_status "You can now start your application with: npm run build && npm start"
