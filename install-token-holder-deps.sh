#!/bin/bash

# Install dependencies for token holder fetching
echo "Installing token holder dependencies..."

# Install Puppeteer and CSV parser
npm install puppeteer csv-parser

echo "Dependencies installed successfully!"
echo ""
echo "Next steps:"
echo "1. Run the database migration: mysql -u your_user -p your_database < db/migrations/add_token_holder_tables.sql"
echo "2. Restart your application: pm2 restart all"
echo "3. Access the admin panel and go to the 'Token Holders' tab"
echo ""
echo "Note: Make sure you have the required system dependencies for Puppeteer:"
echo "sudo apt install -y chromium-browser fonts-liberation libasound2 libatk-bridge2.0-0 libatk1.0-0 libatspi2.0-0 libcups2 libdbus-1-3 libdrm2 libgtk-3-0 libnspr4 libnss3 libxcomposite1 libxdamage1 libxfixes3 libxkbcommon0 libxrandr2 xvfb"
