/**
 * Token validation utilities for checking wallet balances and requirements
 */

import { readContract } from '@wagmi/core';
import { formatUnits, createPublicClient, http } from 'viem';
import { cronos, mainnet, polygon, bsc, arbitrum } from 'viem/chains';
import { config } from '../config';

// Basic ERC20 ABI for balanceOf and decimals functions
const ERC20_ABI = [
  {
    name: "balanceOf",
    type: "function",
    inputs: [{ name: "account", type: "address" }],
    outputs: [{ name: "balance", type: "uint256" }],
    stateMutability: "view",
  },
  {
    name: "decimals",
    type: "function",
    inputs: [],
    outputs: [{ name: "decimals", type: "uint8" }],
    stateMutability: "view",
  },
] as const;

// Chain configurations for server-side RPC calls
const getChainConfig = (chainId: string) => {
  switch (chainId) {
    case '25':
      return {
        chain: cronos,
        rpcUrl: 'https://evm.cronos.org'
      };
    case '1':
      return {
        chain: mainnet,
        rpcUrl: 'https://eth.llamarpc.com'
      };
    case '137':
      return {
        chain: polygon,
        rpcUrl: 'https://polygon.llamarpc.com'
      };
    case '56':
      return {
        chain: bsc,
        rpcUrl: 'https://bsc.llamarpc.com'
      };
    case '42161':
      return {
        chain: arbitrum,
        rpcUrl: 'https://arbitrum.llamarpc.com'
      };
    default:
      // Default to Cronos for unknown chains
      return {
        chain: cronos,
        rpcUrl: 'https://evm.cronos.org'
      };
  }
};

export interface TokenRequirements {
  tokenAddress: string;
  tokenName: string;
  minimumHoldings: string;
  secondaryMinHoldings?: string;
}

export interface FullTokenRequirements {
  mainRequirement?: {
    chainId: string;
    chainName: string;
    tokenAddress: string;
    tokenName: string;
    minimumHoldings: string;
    secondaryMinHoldings?: string;
  };
  secondaryRequirements?: Array<{
    id: string;
    tokenAddress: string;
    tokenName: string;
    minimumHoldings: string;
  }>;
}

/**
 * Check if token requirements are configured (non-zero values)
 */
export function hasTokenRequirements(requirements: TokenRequirements): boolean {
  const minHoldings = parseFloat(requirements.minimumHoldings || '0');

  // If minimum holdings is greater than 0, we have token requirements
  return minHoldings > 0;
}

/**
 * Check if full token requirements are configured (checks both legacy and new structure)
 */
export function hasFullTokenRequirements(fullRequirements: FullTokenRequirements): boolean {
  // Check if main requirement has token address and minimum holdings
  if (fullRequirements.mainRequirement) {
    const mainReq = fullRequirements.mainRequirement;
    if (mainReq.tokenAddress) {
      const minHoldings = parseFloat(mainReq.minimumHoldings || '0');
      if (minHoldings > 0) {
        return true;
      }
    }
  }

  // Check if any secondary requirements exist
  if (fullRequirements.secondaryRequirements && fullRequirements.secondaryRequirements.length > 0) {
    return fullRequirements.secondaryRequirements.some(req => {
      const minHoldings = parseFloat(req.minimumHoldings || '0');
      return minHoldings > 0;
    });
  }

  return false;
}

/**
 * Check if any token requirements are configured (checks both legacy and full structures)
 */
export function hasAnyTokenRequirements(
  legacyRequirements?: TokenRequirements,
  fullRequirements?: FullTokenRequirements
): boolean {
  // First check legacy requirements
  if (legacyRequirements && hasTokenRequirements(legacyRequirements)) {
    return true;
  }

  // Then check full requirements
  if (fullRequirements && hasFullTokenRequirements(fullRequirements)) {
    return true;
  }

  return false;
}

/**
 * Check if only referral code is required (all token values are zero)
 */
export function isReferralOnlyMode(requirements: TokenRequirements): boolean {
  return !hasTokenRequirements(requirements);
}

/**
 * Get the validation mode based on token requirements (legacy function)
 */
export function getValidationMode(requirements: TokenRequirements): 'token' | 'referral' {
  return hasTokenRequirements(requirements) ? 'token' : 'referral';
}

/**
 * Get the validation mode based on both legacy and full token requirements
 */
export function getValidationModeComprehensive(
  legacyRequirements?: TokenRequirements,
  fullRequirements?: FullTokenRequirements
): 'token' | 'referral' {
  return hasAnyTokenRequirements(legacyRequirements, fullRequirements) ? 'token' : 'referral';
}

/**
 * Check if user meets main token requirements
 */
async function checkMainTokenRequirements(
  walletAddress: string,
  mainRequirement: FullTokenRequirements['mainRequirement'],
  chainId: string
): Promise<{
  hasEnoughTokens: boolean;
  currentBalance: string;
  minimumRequired: string;
  error?: string;
}> {
  if (!mainRequirement || !mainRequirement.tokenAddress) {
    return {
      hasEnoughTokens: true,
      currentBalance: '0',
      minimumRequired: '0'
    };
  }

  const minimumRequired = parseFloat(mainRequirement.minimumHoldings || '0');
  if (minimumRequired <= 0) {
    return {
      hasEnoughTokens: true,
      currentBalance: '0',
      minimumRequired: '0'
    };
  }

  try {
    // Use server-side function if we're in a server context (no window object)
    const isServerSide = typeof window === 'undefined';
    const balance = isServerSide
      ? await getTokenBalanceServerSide(walletAddress, mainRequirement.tokenAddress, chainId)
      : await getTokenBalance(walletAddress, mainRequirement.tokenAddress, chainId);

    const hasEnough = parseFloat(balance.formatted) >= minimumRequired;

    console.log(`[checkMainTokenRequirements] DEBUG:`);
    console.log(`  - Required: ${minimumRequired} (${minimumRequired.toExponential()})`);
    console.log(`  - User balance: ${balance.formatted}`);
    console.log(`  - Has enough: ${hasEnough}`);

    return {
      hasEnoughTokens: hasEnough,
      currentBalance: balance.formatted,
      minimumRequired: minimumRequired.toString()
    };
  } catch (error) {
    return {
      hasEnoughTokens: false,
      currentBalance: '0',
      minimumRequired: minimumRequired.toString(),
      error: error instanceof Error ? error.message : 'Failed to check main token balance'
    };
  }
}

/**
 * Check if user meets any secondary token requirements
 */
async function checkSecondaryTokenRequirements(
  walletAddress: string,
  secondaryRequirements: FullTokenRequirements['secondaryRequirements'],
  chainId: string
): Promise<{
  hasEnoughTokens: boolean;
  currentBalance: string;
  minimumRequired: string;
  tokenName: string;
  error?: string;
} | null> {
  if (!secondaryRequirements || secondaryRequirements.length === 0) {
    return null;
  }

  // Check each secondary token requirement and track balances
  let bestTokenResult: {
    currentBalance: string;
    minimumRequired: string;
    tokenName: string;
  } | null = null;

  for (const requirement of secondaryRequirements) {
    const minimumRequired = parseFloat(requirement.minimumHoldings || '0');
    if (minimumRequired <= 0) continue;

    try {
      // Use server-side function if we're in a server context (no window object)
      const isServerSide = typeof window === 'undefined';
      const balance = isServerSide
        ? await getTokenBalanceServerSide(walletAddress, requirement.tokenAddress, chainId)
        : await getTokenBalance(walletAddress, requirement.tokenAddress, chainId);
      const hasEnough = parseFloat(balance.formatted) >= minimumRequired;

      // Keep track of the token with the highest balance for display purposes
      if (!bestTokenResult || parseFloat(balance.formatted) > parseFloat(bestTokenResult.currentBalance)) {
        bestTokenResult = {
          currentBalance: balance.formatted,
          minimumRequired: minimumRequired.toString(),
          tokenName: requirement.tokenName
        };
      }

      if (hasEnough) {
        return {
          hasEnoughTokens: true,
          currentBalance: balance.formatted,
          minimumRequired: minimumRequired.toString(),
          tokenName: requirement.tokenName
        };
      }
    } catch (error) {
      console.error(`Error checking secondary token ${requirement.tokenName}:`, error);
      // Continue checking other tokens
    }
  }

  // If we get here, none of the secondary requirements were met
  // Return the best token result (highest balance) or fallback to first token
  const fallbackResult = bestTokenResult || {
    currentBalance: '0',
    minimumRequired: secondaryRequirements[0]?.minimumHoldings || '0',
    tokenName: secondaryRequirements[0]?.tokenName || 'Unknown'
  };

  return {
    hasEnoughTokens: false,
    currentBalance: fallbackResult.currentBalance,
    minimumRequired: fallbackResult.minimumRequired,
    tokenName: fallbackResult.tokenName,
    error: 'No secondary token requirements met'
  };
}

/**
 * Check if user meets secondary minimum holdings for main token
 */
async function checkSecondaryMinHoldings(
  walletAddress: string,
  mainRequirement: FullTokenRequirements['mainRequirement'],
  chainId: string
): Promise<{
  hasEnoughTokens: boolean;
  currentBalance: string;
  minimumRequired: string;
  error?: string;
}> {
  if (!mainRequirement || !mainRequirement.tokenAddress || !mainRequirement.secondaryMinHoldings) {
    return {
      hasEnoughTokens: true,
      currentBalance: '0',
      minimumRequired: '0'
    };
  }

  const minimumRequired = parseFloat(mainRequirement.secondaryMinHoldings);
  if (minimumRequired <= 0) {
    return {
      hasEnoughTokens: true,
      currentBalance: '0',
      minimumRequired: '0'
    };
  }

  try {
    // Use server-side function if we're in a server context (no window object)
    const isServerSide = typeof window === 'undefined';
    const balance = isServerSide
      ? await getTokenBalanceServerSide(walletAddress, mainRequirement.tokenAddress, chainId)
      : await getTokenBalance(walletAddress, mainRequirement.tokenAddress, chainId);
    const hasEnough = parseFloat(balance.formatted) >= minimumRequired;

    return {
      hasEnoughTokens: hasEnough,
      currentBalance: balance.formatted,
      minimumRequired: minimumRequired.toString()
    };
  } catch (error) {
    return {
      hasEnoughTokens: false,
      currentBalance: '0',
      minimumRequired: minimumRequired.toString(),
      error: error instanceof Error ? error.message : 'Failed to check secondary min holdings'
    };
  }
}

/**
 * Get token balance from blockchain
 */
async function getTokenBalance(
  walletAddress: string,
  tokenAddress: string,
  chainId: string
): Promise<{
  raw: string;
  formatted: string;
  decimals: number;
}> {
  try {
    console.log(`[getTokenBalance] Checking balance for wallet: ${walletAddress}, token: ${tokenAddress}, chain: ${chainId}`);

    if (!walletAddress || !tokenAddress) {
      console.log(`[getTokenBalance] Invalid parameters: wallet=${walletAddress}, token=${tokenAddress}`);
      return {
        raw: '0',
        formatted: '0.00',
        decimals: 18
      };
    }



    // Get token decimals first
    let decimals = 18; // Default to 18 decimals
    try {
      const decimalResult = await readContract(config, {
        address: tokenAddress as `0x${string}`,
        abi: ERC20_ABI,
        functionName: "decimals",
        chainId: parseInt(chainId)
      });
      decimals = Number(decimalResult);
      console.log(`[getTokenBalance] Token decimals: ${decimals}`);
    } catch (decimalError) {
      console.warn(`[getTokenBalance] Error fetching decimals for token ${tokenAddress}, using default 18:`, decimalError);
    }

    // Get token balance
    const tokenBalance = await readContract(config, {
      address: tokenAddress as `0x${string}`,
      abi: ERC20_ABI,
      functionName: "balanceOf",
      args: [walletAddress as `0x${string}`],
      chainId: parseInt(chainId)
    }) as bigint;

    console.log(`[getTokenBalance] Raw token balance: ${tokenBalance.toString()}`);

    const formatted = formatUnits(tokenBalance, decimals);
    const formattedBalance = parseFloat(formatted).toFixed(2);

    console.log(`[getTokenBalance] Formatted balance: ${formattedBalance}`);

    return {
      raw: tokenBalance.toString(),
      formatted: formattedBalance,
      decimals: decimals
    };
  } catch (error) {
    console.error(`[getTokenBalance] Error fetching token balance for ${walletAddress}:`, error);
    return {
      raw: '0',
      formatted: '0.00',
      decimals: 18
    };
  }
}

/**
 * Get token balance from blockchain (server-side compatible)
 * This function uses direct RPC calls instead of wagmi config
 */
async function getTokenBalanceServerSide(
  walletAddress: string,
  tokenAddress: string,
  chainId: string
): Promise<{
  raw: string;
  formatted: string;
  decimals: number;
}> {
  try {
    console.log(`[getTokenBalanceServerSide] Checking balance for wallet: ${walletAddress}, token: ${tokenAddress}, chain: ${chainId}`);

    if (!walletAddress || !tokenAddress) {
      console.log(`[getTokenBalanceServerSide] Invalid parameters: wallet=${walletAddress}, token=${tokenAddress}`);
      return {
        raw: '0',
        formatted: '0.00',
        decimals: 18
      };
    }

    const chainConfig = getChainConfig(chainId);
    const publicClient = createPublicClient({
      chain: chainConfig.chain,
      transport: http(chainConfig.rpcUrl)
    });

    // Get token decimals first
    let decimals = 18; // Default to 18 decimals
    try {
      const decimalResult = await publicClient.readContract({
        address: tokenAddress as `0x${string}`,
        abi: ERC20_ABI,
        functionName: "decimals"
      });
      decimals = Number(decimalResult);
      console.log(`[getTokenBalanceServerSide] Token decimals: ${decimals}`);
    } catch (decimalError) {
      console.warn(`[getTokenBalanceServerSide] Error fetching decimals for token ${tokenAddress}, using default 18:`, decimalError);
    }

    // Get token balance
    const tokenBalance = await publicClient.readContract({
      address: tokenAddress as `0x${string}`,
      abi: ERC20_ABI,
      functionName: "balanceOf",
      args: [walletAddress as `0x${string}`]
    }) as bigint;

    console.log(`[getTokenBalanceServerSide] Raw token balance: ${tokenBalance.toString()}`);

    const formatted = formatUnits(tokenBalance, decimals);
    const formattedBalance = parseFloat(formatted).toFixed(2);

    console.log(`[getTokenBalanceServerSide] Formatted balance: ${formattedBalance}`);

    return {
      raw: tokenBalance.toString(),
      formatted: formattedBalance,
      decimals: decimals
    };
  } catch (error) {
    console.error(`[getTokenBalanceServerSide] Error fetching token balance for ${walletAddress}:`, error);
    return {
      raw: '0',
      formatted: '0.00',
      decimals: 18
    };
  }
}

/**
 * Main function to check wallet balance against token requirements
 * Implements the logic: Main token → Secondary tokens → Secondary min holdings
 */
export async function checkWalletBalanceWithRequirements(
  walletAddress: string,
  fullRequirements: FullTokenRequirements,
  chainId: string
): Promise<{
  canProceed: boolean;
  validationPath: 'main' | 'secondary' | 'none';
  mainTokenResult?: {
    hasEnoughTokens: boolean;
    currentBalance: string;
    minimumRequired: string;
    error?: string;
  };
  secondaryTokenResult?: {
    hasEnoughTokens: boolean;
    currentBalance: string;
    minimumRequired: string;
    tokenName: string;
    error?: string;
  };
  secondaryMinHoldingsResult?: {
    hasEnoughTokens: boolean;
    currentBalance: string;
    minimumRequired: string;
    error?: string;
  };
  error?: string;
}> {
  try {
    // Step 1: Check main token requirements
    const mainResult = await checkMainTokenRequirements(
      walletAddress,
      fullRequirements.mainRequirement,
      chainId
    );

    if (mainResult.hasEnoughTokens) {
      return {
        canProceed: true,
        validationPath: 'main',
        mainTokenResult: mainResult
      };
    }

    // Step 2: If main token fails, check secondary token requirements
    const secondaryResult = await checkSecondaryTokenRequirements(
      walletAddress,
      fullRequirements.secondaryRequirements,
      chainId
    );

    if (!secondaryResult || !secondaryResult.hasEnoughTokens) {
      return {
        canProceed: false,
        validationPath: 'none',
        mainTokenResult: mainResult,
        secondaryTokenResult: secondaryResult || undefined,
        error: 'Neither main token nor secondary token requirements met'
      };
    }

    // Step 3: If secondary token passes, check secondary min holdings for main token
    const secondaryMinResult = await checkSecondaryMinHoldings(
      walletAddress,
      fullRequirements.mainRequirement,
      chainId
    );

    // Both secondary token AND secondary min holdings must pass
    if (!secondaryMinResult.hasEnoughTokens) {
      return {
        canProceed: false,
        validationPath: 'none',
        mainTokenResult: mainResult,
        secondaryTokenResult: secondaryResult,
        secondaryMinHoldingsResult: secondaryMinResult,
        error: 'Secondary token requirement met but insufficient main token secondary minimum holdings'
      };
    }

    // Both secondary token and secondary min holdings pass
    return {
      canProceed: true,
      validationPath: 'secondary',
      mainTokenResult: mainResult,
      secondaryTokenResult: secondaryResult,
      secondaryMinHoldingsResult: secondaryMinResult
    };

  } catch (error) {
    return {
      canProceed: false,
      validationPath: 'none',
      error: error instanceof Error ? error.message : 'Failed to check wallet balance'
    };
  }
}

/**
 * Legacy function for backward compatibility
 * Check wallet token balance (now uses the new validation logic)
 */
export async function checkWalletBalance(
  walletAddress: string,
  tokenAddress: string,
  chainId: string,
  minimumRequired: string
): Promise<{
  hasEnoughTokens: boolean;
  currentBalance: string;
  minimumRequired: string;
  error?: string;
}> {
  try {
    if (!tokenAddress || parseFloat(minimumRequired) <= 0) {
      return {
        hasEnoughTokens: true,
        currentBalance: '0',
        minimumRequired: '0'
      };
    }

    const balance = await getTokenBalance(walletAddress, tokenAddress, chainId);
    const hasEnough = parseFloat(balance.formatted) >= parseFloat(minimumRequired);

    return {
      hasEnoughTokens: hasEnough,
      currentBalance: balance.formatted,
      minimumRequired
    };
  } catch (error) {
    return {
      hasEnoughTokens: false,
      currentBalance: '0',
      minimumRequired,
      error: error instanceof Error ? error.message : 'Failed to check wallet balance'
    };
  }
}

/**
 * Format token amount for display
 */
export function formatTokenAmount(amount: string, _decimals: number = 18): string {
  const num = parseFloat(amount);
  if (num === 0) return '0';
  if (num < 1) return num.toFixed(6);
  if (num < 1000) return num.toFixed(2);
  if (num < 1000000) return `${(num / 1000).toFixed(1)}K`;
  return `${(num / 1000000).toFixed(1)}M`;
}

/**
 * Validate referral code format
 */
export function isValidReferralCode(code: string): boolean {
  // Referral codes should be in format 'w3txxxxx' (8 characters total)
  const referralRegex = /^w3t[a-zA-Z0-9]{5}$/;
  return referralRegex.test(code);
}
