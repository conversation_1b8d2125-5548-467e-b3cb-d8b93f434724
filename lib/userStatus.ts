"use client";

// Cache for user status to avoid repeated fetches
let userStatusCache: Record<string, string> = {};

/**
 * Fetch user status from the database
 * @param address User's wallet address
 * @returns Promise with user status
 */
export async function fetchUserStatus(address: string): Promise<{
  status: string;
  isApproved: boolean;
}> {
  // Return from cache if available
  if (userStatusCache[address]) {
    return {
      status: userStatusCache[address],
      isApproved: userStatusCache[address] === 'approved'
    };
  }

  try {
    // Fetch user status from API
    const response = await fetch(`/api/profile/status?address=${address}`);

    if (!response.ok) {
      try {
        const errorData = await response.json();
        if (errorData.error && errorData.error.includes('Database is currently unavailable')) {
          throw new Error('Database is currently unavailable. Please try again later.');
        } else {
          throw new Error(`Failed to fetch user status: ${response.statusText}`);
        }
      } catch (e) {
        // If we can't parse JSON, just use the status text
        throw new Error(`Failed to fetch user status: ${response.statusText}`);
      }
    }

    const data = await response.json();

    // Cache the result
    userStatusCache[address] = data.status;

    return {
      status: data.status,
      isApproved: data.status === 'approved'
    };
  } catch (error) {
    console.error(`Error fetching user status for ${address}:`, error);

    // Return default values on error
    return {
      status: 'new',
      isApproved: false
    };
  }
}

/**
 * Clear the user status cache
 */
export function clearUserStatusCache() {
  userStatusCache = {};
}
