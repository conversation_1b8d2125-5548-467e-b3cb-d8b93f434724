"use client";

// Client-side utility to fetch token requirements

interface TokenRequirement {
  tokenAddress: string;
  tokenName: string;
  minimumHoldings: string;
  secondaryMinHoldings?: string;
}

// Cache for token requirements to avoid repeated fetches
let tokenRequirementsCache: Record<string, TokenRequirement> = {};

/**
 * Fetch token requirements for a specific chain
 * @param chainId The chain ID to get requirements for
 * @returns Promise with token requirements
 */
export async function fetchTokenRequirements(chainId: string): Promise<TokenRequirement> {
  // Skip cache to ensure fresh data - TODO: implement proper cache invalidation
  // if (tokenRequirementsCache[chainId]) {
  //   return tokenRequirementsCache[chainId];
  // }

  try {
    // Fetch token requirements from API
    const response = await fetch(`/api/token-requirements/${chainId}`);

    if (!response.ok) {
      throw new Error(`Failed to fetch token requirements: ${response.statusText}`);
    }

    const data = await response.json();

    // Cache the result
    tokenRequirementsCache[chainId] = data;

    return data;
  } catch (error) {
    console.error(`Error fetching token requirements for chain ${chainId}:`, error);

    // Return empty values on error - no fallbacks
    return {
      tokenAddress: '',
      tokenName: 'Web3Tools',
      minimumHoldings: '0',
      secondaryMinHoldings: '0'
    };
  }
}

/**
 * Clear the token requirements cache
 */
export function clearTokenRequirementsCache() {
  tokenRequirementsCache = {};
}
