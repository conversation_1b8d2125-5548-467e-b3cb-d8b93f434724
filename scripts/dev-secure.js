#!/usr/bin/env node

/**
 * Secure development server that filters wallet addresses from all output
 */

// Override console methods BEFORE loading Next.js
const originalLog = console.log;
const originalError = console.error;
const originalWarn = console.warn;
const originalInfo = console.info;

// Function to sanitize any string
function sanitizeString(str) {
  if (typeof str !== 'string') return str;
  
  // Replace Ethereum addresses (0x + 40 hex chars)
  str = str.replace(/0x[a-fA-F0-9]{40}/g, '[WALLET_ADDRESS]');
  
  // Replace what looks like Solana addresses in URL contexts
  str = str.replace(/([?&=])([1-9A-HJ-NP-Za-km-z]{32,44})(?=[&\s\n\r'"}]|$)/g, '$1[WALLET_ADDRESS]');
  
  return str;
}

// Function to sanitize arguments
function sanitizeArgs(args) {
  return args.map(arg => {
    if (typeof arg === 'string') {
      return sanitizeString(arg);
    } else if (typeof arg === 'object' && arg !== null) {
      try {
        const str = JSON.stringify(arg, null, 2);
        const sanitized = sanitizeString(str);
        return sanitized !== str ? JSON.parse(sanitized) : arg;
      } catch (e) {
        return arg;
      }
    }
    return arg;
  });
}

// Override console methods
console.log = function(...args) {
  originalLog.apply(console, sanitizeArgs(args));
};

console.error = function(...args) {
  originalError.apply(console, sanitizeArgs(args));
};

console.warn = function(...args) {
  originalWarn.apply(console, sanitizeArgs(args));
};

console.info = function(...args) {
  originalInfo.apply(console, sanitizeArgs(args));
};

// Set environment variables
process.env.NEXT_TELEMETRY_DISABLED = '1';
process.env.NEXT_PRIVATE_DEBUG_CACHE = 'false';

console.log('🔒 Secure development mode activated');
console.log('🔒 All wallet addresses will be filtered from logs');
console.log('');

// Now start Next.js
const { spawn } = require('child_process');

const nextProcess = spawn('npx', ['next', 'dev', '--turbopack'], {
  stdio: 'inherit',
  env: process.env,
  cwd: process.cwd()
});

nextProcess.on('close', (code) => {
  process.exit(code);
});

nextProcess.on('error', (error) => {
  console.error('Failed to start Next.js:', error);
  process.exit(1);
});

// Handle Ctrl+C
process.on('SIGINT', () => {
  nextProcess.kill('SIGINT');
});

process.on('SIGTERM', () => {
  nextProcess.kill('SIGTERM');
});
