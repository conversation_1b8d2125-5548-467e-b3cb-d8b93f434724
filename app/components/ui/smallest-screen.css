/* Custom styles for the smallest screens */
@media (max-width: 375px) {
  /* XS-specific styles */
  .xs\:mr-2 {
    margin-right: 0.5rem !important;
  }
  /* Container styles */
  .content-container {
    width: calc(100% - 8px) !important; /* Leave space on right */
    padding-right: 12px !important; /* Add padding for text */
    margin-right: 0 !important; /* No margin */
  }

  /* Text container styles */
  .text-container {
    padding-right: 20px !important; /* Add more padding for text */
    width: calc(100% - 20px) !important; /* Make room for text */
  }

  /* Adjust max-width for text content */
  .text-container > div {
    max-width: 100% !important;
  }

  /* Wrapper styles */
  .container-wrapper {
    padding-right: 0 !important;
    margin-right: 0 !important;
    width: 100% !important; /* Full width */
  }

  /* Ensure scrollbar is flush with right edge */
  .custom-scrollbar {
    width: 100% !important; /* Full width */
  }

  .custom-scrollbar::-webkit-scrollbar {
    position: fixed !important;
    right: 0 !important; /* Flush with edge */
    width: 4px !important; /* Visible scrollbar */
  }
}

/* Even smaller screens */
@media (max-width: 320px) {
  /* Container styles */
  .content-container {
    width: calc(100% - 10px) !important; /* Leave more space on right */
    padding-right: 14px !important; /* More padding for text */
    margin-right: 0 !important; /* No margin */
  }

  /* Text container styles */
  .text-container {
    padding-right: 24px !important; /* Add even more padding for text */
    width: calc(100% - 24px) !important; /* Make more room for text */
  }

  /* Adjust max-width for text content */
  .text-container > div {
    max-width: 100% !important;
  }

  /* Wrapper styles */
  .container-wrapper {
    padding-right: 0 !important;
    margin-right: 0 !important;
    width: 100% !important; /* Full width */
  }

  /* Ensure scrollbar is flush with right edge */
  .custom-scrollbar {
    width: 100% !important; /* Full width */
  }

  .custom-scrollbar::-webkit-scrollbar {
    position: fixed !important;
    right: 0 !important; /* Flush with edge */
    width: 3px !important; /* Thin scrollbar */
  }
}
