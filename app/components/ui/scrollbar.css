/* Custom scrollbar styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px; /* Thinner scrollbar for a more flush appearance */
  height: 0px; /* Hide horizontal scrollbar */
  display: block !important;
}

@media (max-width: 640px) {
  .custom-scrollbar::-webkit-scrollbar {
    width: 3px; /* Very thin on mobile for flush appearance */
    position: absolute;
    right: 0;
  }

  /* Force the scrollbar to be at the edge */
  .custom-scrollbar {
    padding-right: 0 !important;
    margin-right: 0 !important;
    box-sizing: border-box !important;
    position: relative !important;
    width: 100% !important; /* Full width */
  }

  /* XS-specific styles */
  .xs\:mr-2 {
    margin-right: 0.5rem !important;
  }
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  display: block !important;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(113, 113, 122, 0.8); /* Darker for better visibility */
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: block !important;
}

@media (max-width: 640px) {
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(113, 113, 122, 1); /* Fully opaque on mobile for better visibility when flush */
    border-radius: 0; /* No border radius for flush appearance */
    border: none; /* No border for flush appearance */
    right: 0; /* Force alignment to the right */
    border-left: 0; /* No left border for flush appearance */
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent; /* Transparent track for separation effect */
    position: absolute !important;
    right: 0 !important;
    margin-right: 0 !important;
    border-left: 0; /* No left border for flush appearance */
  }
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(113, 113, 122, 1);
}

/* For Firefox */
.custom-scrollbar {
  scrollbar-width: thin; /* Use thin scrollbar for Firefox */
  scrollbar-color: rgba(113, 113, 122, 0.8) rgba(0, 0, 0, 0.2);
  overflow-x: hidden !important;
  overflow-y: scroll !important; /* Force vertical scrollbar to always show */
}

@media (max-width: 640px) {
  .custom-scrollbar {
    scrollbar-width: thin; /* Thin scrollbar for Firefox on mobile */
    scrollbar-color: rgba(113, 113, 122, 1) transparent; /* Match the webkit colors with transparent track */
    padding-right: 0 !important;
    margin-right: 0 !important;
    padding-left: 6px !important; /* Create visual separation */
  }
}
