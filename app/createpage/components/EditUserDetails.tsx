'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
// import { Switch } from '@/components/ui/switch';
import { toast } from 'sonner';
import ComponentVisibilityToggle from './ComponentVisibilityToggle';
import SocialLinksEditor from './SocialLinksEditor';
import ColorPicker from './ColorPicker';
import FontColorPicker from './FontColorPicker';
import {
  Save,
  Loader2
} from 'lucide-react';
import Image from 'next/image';

interface EditUserDetailsProps {
  address: string;
  initialData?: any; // Optional initial data from parent component
  hidden: string;
}

interface SocialLink {
  platform: string;
  url: string;
  customLabel?: string;
}

interface SocialLinks {
  [key: string]: string;
}

interface UserDetailsData {
  // name and bio removed - now handled in profile picture component
  socialLinks: SocialLinks;
}

export default function EditUserDetails({ address, initialData, hidden }: EditUserDetailsProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [initialUserDetails, setInitialUserDetails] = useState<UserDetailsData | null>(null);
  const [isHidden, setIsHidden] = useState(hidden === 'Y');
  const [backgroundColor, setBackgroundColor] = useState('transparent');
  const [fontColor, setFontColor] = useState<string>('#ffffff');
  const [userDetails, setUserDetails] = useState<UserDetailsData>({
    socialLinks: {
      twitter: '',
      facebook: '',
      discord: '',
      youtube: '',
      website: '',
      email: '',
      telegram: '',
      crypto: ''
    }
  });

  // Update isHidden state when hidden prop changes
  useEffect(() => {
    setIsHidden(hidden === 'Y');
  }, [hidden]);

  // Load user details from initialData or API
  useEffect(() => {
    if (initialData) {
      console.log('EditUserDetails received initialData:', initialData);
      // Parse social links if they're a string
      let socialLinks = initialData.socialLinks;
      console.log('Social links from initialData:', socialLinks);
      if (typeof socialLinks === 'string') {
        try {
          socialLinks = JSON.parse(socialLinks);
          console.log('Parsed social links:', socialLinks);
        } catch (error) {
          console.error('Failed to parse social links:', error);
          socialLinks = {};
        }
      }

      // If initialData is provided, use it
      const details = {
        socialLinks: socialLinks || {
          twitter: '',
          discord: '',
          telegram: '',
          website: '',
          facebook: '',
          youtube: '',
          email: '',
          linkedin: '',
          cro: ''
        }
      };

      setUserDetails(details);
      setInitialUserDetails(JSON.parse(JSON.stringify(details))); // Deep copy for comparison
    } else if (address) {
      // Otherwise, fetch from API
      const fetchUserDetails = async () => {
        setIsLoading(true);
        try {
          const response = await fetch(`/api/profile/${address}`);
          if (response.ok) {
            const data = await response.json();

            // Parse social links if they're a string
            let socialLinks = data.socialLinks;
            if (typeof socialLinks === 'string') {
              try {
                socialLinks = JSON.parse(socialLinks);
              } catch (error) {
                console.error('Failed to parse social links:', error);
                socialLinks = {};
              }
            }

            const details = {
              socialLinks: socialLinks || {
                twitter: '',
                discord: '',
                telegram: '',
                website: '',
                facebook: '',
                youtube: '',
                email: '',
                linkedin: '',
                cro: ''
              }
            };

            setUserDetails(details);
            setInitialUserDetails(JSON.parse(JSON.stringify(details))); // Deep copy for comparison
          }
        } catch (error) {
          console.error('Failed to fetch user details:', error);
        } finally {
          setIsLoading(false);
        }
      };

      fetchUserDetails();
    }
  }, [address, initialData]);

  // Check if there are unsaved changes
  const checkForChanges = (updatedDetails: UserDetailsData) => {
    if (!initialUserDetails) return false;

    // Compare social links only - name and bio are now handled in profile picture component
    const initialLinks = JSON.stringify(initialUserDetails.socialLinks || {});
    const currentLinks = JSON.stringify(updatedDetails.socialLinks || {});

    return initialLinks !== currentLinks;
  };

  // Handle input changes - removed since name and bio are now handled in profile picture component
  // This function is kept for reference but not used
  const handleInputChange = (field: string, value: string) => {
    // No-op - fields are now handled in profile picture component
  };

  // Handle social link changes
  const handleSocialLinkChange = (platform: keyof SocialLinks, value: string) => {
    const updatedDetails = {
      ...userDetails,
      socialLinks: {
        ...userDetails.socialLinks,
        [platform]: value
      }
    };

    setUserDetails(updatedDetails);
    setHasUnsavedChanges(checkForChanges(updatedDetails));
  };

  // Handle visibility change from the toggle component
  const handleVisibilityChange = (componentType: string, hidden: string) => {
    setIsHidden(hidden === 'Y');
  };

  // Save user details
  const handleSaveDetails = async () => {
    if (!address) return;

    setIsSaving(true);
    try {
      // Prepare data for API - name and bio are now handled in profile picture component
      const profileData = {
        address,
        socialLinks: userDetails.socialLinks,
        backgroundColor,
        fontColor
      };

      // Send to API
      const response = await fetch('/api/profile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(profileData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to save profile details');
      }

      setHasUnsavedChanges(false);
      toast.success('Profile details saved successfully');
    } catch (error) {
      console.error('Error saving profile details:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to save profile details');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="relative border border-neutral-800 overflow-hidden mt-4">
      {/* Visibility Toggle */}
      <div className="absolute top-2 right-2 z-10">
        <ComponentVisibilityToggle
          componentType="socialLinks"
          address={address}
          initialHidden={hidden}
          onChange={handleVisibilityChange}
          minimal={true}
        />
      </div>

      {/* Color Pickers */}
      <ColorPicker
        address={address}
        componentType="socialLinks"
        onColorChange={setBackgroundColor}
      />

      <FontColorPicker
        address={address}
        componentType="socialLinks"
        onColorChange={setFontColor}
      />

      <div className="p-6">
        {/* Profile Details heading removed */}
        <div className="space-y-6">
          {/* Profile name field removed - now handled in profile picture component */}

          {/* Bio field removed - now handled in profile picture component */}

          <div className="space-y-4 mt-10">

            <SocialLinksEditor
              initialLinks={userDetails.socialLinks}
              onChange={(links) => {
                // Only update if the links have actually changed
                if (JSON.stringify(links) !== JSON.stringify(userDetails.socialLinks)) {
                  const updatedDetails = { ...userDetails, socialLinks: links };
                  setUserDetails(updatedDetails);
                  setHasUnsavedChanges(checkForChanges(updatedDetails));
                }
              }}
              disabled={isLoading}
              backgroundColor={backgroundColor}
              fontColor={fontColor}
            />
          </div>

          <div className="flex justify-center mt-6 mb-3">
            <Button
              onClick={handleSaveDetails}
              disabled={isLoading || isSaving || !hasUnsavedChanges}
              className="w-full max-w-xs"
            >
              {isSaving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Social Links
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
