'use client';

import { useState, useEffect } from 'react';
import { useAppKitAccount, useAppKitNetwork } from '@reown/appkit/react';
import { usePathname } from 'next/navigation';
import WalletValidator from '@/components/WalletValidator';
import { toast } from 'sonner';
// Import removed: createProfile is now called via API
// Old components removed
import EditUserDetails from './components/EditUserDetails';
import EditHero from './components/EditHero';
import EditBannerPfp from './components/EditBannerPfp';
import { Loader2 } from 'lucide-react';
import { HeaderTypewriterEffect } from '@/components/ui/header-typewriter-effect';
import { useMetadata } from '@/app/contexts/MetadataContext';

export default function CreatePage() {
  const { address, isConnected } = useAppKitAccount();
  const { chainId } = useAppKitNetwork();
  const pathname = usePathname();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [profileData, setProfileData] = useState<any>(null);
  const { fetchBannerPfpMetadata } = useMetadata();

  // Old component states removed

  // BannerPfp state
  const [bannerPfpState, setBannerPfpState] = useState({
    bannerUrl: null as string | null,
    bannerScale: 1,
    bannerPosition: { x: 0, y: 0 },
    bannerNaturalSize: null as { width: number; height: number } | null,
    profileUrl: null as string | null,
    profileScale: 1,
    profilePosition: { x: 0, y: 0 },
    profileNaturalSize: null as { width: number; height: number } | null,
    profileShape: 'circular' as 'circular' | 'rectangular' | 'squarish',
    profileHorizontalPosition: 50,
    profileName: '',
    profileBio: '',
    urlName: '', // Add urlName field with default empty string
    profileNameHorizontalPosition: 50,
    profileNameStyle: {
      fontSize: '1.5rem',
      fontWeight: 'bold',
      fontColor: '#ffffff',
      effect: 'typewriter'
    }
  });

  // Load profile data when wallet is connected or address changes
  useEffect(() => {
    // Reset states when address changes
    setBannerPfpState({
      bannerUrl: null,
      bannerScale: 1,
      bannerPosition: { x: 0, y: 0 },
      bannerNaturalSize: null,
      profileUrl: null,
      profileScale: 1,
      profilePosition: { x: 0, y: 0 },
      profileNaturalSize: null,
      profileShape: 'circular',
      profileHorizontalPosition: 50,
      profileName: '',
      profileBio: '',
      urlName: '', // Add urlName field with default empty string
      profileNameHorizontalPosition: 50,
      profileNameStyle: {
        fontSize: '1.5rem',
        fontWeight: 'bold',
        fontColor: '#ffffff',
        effect: 'typewriter' as 'typewriter' | 'none'
      }
    });

    setProfileData(null);
    setError(null);
    setIsLoading(true);

    console.log('Address changed to:', address);

    const loadProfileData = async () => {
      try {
        console.log('Starting to load profile data...');

        // Ensure profile exists via API call
        console.log('Ensuring profile exists for address:', address);
        if (address && chainId) {
          // Call the API endpoint to ensure defaults instead of calling createProfile directly
          const ensureResponse = await fetch(`/api/ensure-defaults/${address}?chain=${chainId.toString()}`);
          if (!ensureResponse.ok) {
            console.error('Failed to ensure profile exists:', await ensureResponse.text());
          } else {
            console.log('Profile existence check completed');
          }
        } else {
          console.log('No address or chainId available, skipping profile creation');
          return;
        }

        // Fetch the profile data
        console.log(`Loading profile data for address: ${address}`);
        const response = await fetch(`/api/profile/${address}`);
        console.log('Profile API response status:', response.status);

        if (!response.ok) {
          try {
            const errorData = await response.json();
            console.error('Failed to load profile data:', errorData);

            // Check for database unavailable message
            if (errorData.error && errorData.error.includes('Database is currently unavailable')) {
              setError('Database is currently unavailable. Please try again later.');
            } else {
              setError('Failed to load profile data');
            }
          } catch (e) {
            // If we can't parse JSON, just use the status text
            console.error('Failed to load profile data:', response.statusText);
            setError('Failed to load profile data');
          }
          return;
        }

        // Get the profile data with components
        const data = await response.json();
        console.log('Profile data loaded:', data);
        console.log('Components received:', data.components ? data.components.length : 0);

        // Sort components by order
        if (data.components && Array.isArray(data.components)) {
          console.log('Sorting components by order');
          data.components = data.components.sort((a: any, b: any) =>
            parseInt(a.order) - parseInt(b.order)
          );
          console.log('Components after sorting:', data.components);
        } else {
          console.warn('No components array found in profile data or it is not an array');
        }

        setProfileData(data);

        // Only load bannerpfp component
        const bannerPfpComponent = data.components?.find((c: any) => c.componentType === 'bannerpfp');

        // Load bannerpfp from API
        if (bannerPfpComponent) {
          try {
            console.log('Loading bannerpfp for address:', address);
            // Fetch bannerpfp data using the metadata context
            const bannerPfpData = await fetchBannerPfpMetadata(address);
            if (bannerPfpData) {
              console.log('BannerPfp data loaded:', bannerPfpData);
              // Update all bannerpfp state properties
              setBannerPfpState({
                bannerUrl: bannerPfpData.bannerUrl || null,
                bannerScale: bannerPfpData.bannerScale || 1,
                bannerPosition: bannerPfpData.bannerPosition || { x: 0, y: 0 },
                bannerNaturalSize: bannerPfpData.bannerNaturalSize || null,
                profileUrl: bannerPfpData.profileUrl || null,
                profileScale: bannerPfpData.profileScale || 1,
                profilePosition: bannerPfpData.profilePosition || { x: 0, y: 0 },
                profileNaturalSize: bannerPfpData.profileNaturalSize || null,
                profileShape: bannerPfpData.profileShape || 'circular',
                profileHorizontalPosition: bannerPfpData.profileHorizontalPosition || 50,
                profileName: bannerPfpData.profileName || '',
                profileBio: bannerPfpData.profileBio || '',
                urlName: bannerPfpData.urlName || '', // Add urlName field
                profileNameHorizontalPosition: bannerPfpData.profileNameHorizontalPosition || 50,
                profileNameStyle: bannerPfpData.profileNameStyle || {
                  fontSize: '1.5rem',
                  fontWeight: 'bold',
                  fontColor: '#ffffff',
                  effect: 'typewriter' as 'typewriter' | 'none'
                }
              });
              console.log('URL name from API in createpage:', bannerPfpData.urlName);
            }
          } catch (error) {
            console.error('Failed to load bannerpfp:', error);
          }
        }
      } catch (error) {
        console.error('Error loading profile data:', error);
        setError('Error loading profile data');
      } finally {
        setIsLoading(false);
      }
    };

    if (address) {
      loadProfileData();
    }
  }, [address, chainId, pathname]);

  // Define words for the typewriter effect
  const words = [
    { text: "Create" },
    { text: "Your" },
    { text: "Web3" },
    { text: "Socials", className: "text-blue-500 dark:text-blue-500" },
  ];

  return (
    <main className="min-h-screen">
      <div className="container max-w-4xl mx-auto px-4 pt-2 pb-8">
        <div className="text-center mb-2">
          <div className="h-16 sm:h-20 md:h-24 flex items-center justify-center">
            <HeaderTypewriterEffect words={words} />
          </div>
          <p className="text-neutral-500 text-sm max-w-lg mx-auto mt-1">
            Customize your profile by adding a banner, profile picture, and personal details.
          </p>
        </div>
        <WalletValidator>
          {isLoading ? (
            <div className="flex justify-center items-center min-h-[400px]">
              <div className="flex flex-col items-center gap-4">
                <Loader2 className="h-12 w-12 animate-spin text-blue-500" />
                <p className="text-neutral-400">Loading your profile data...</p>
              </div>
            </div>
          ) : error ? (
            <div className="flex justify-center items-center min-h-[400px]">
              <div className="bg-red-900/20 border border-red-900/50 rounded-lg p-6 max-w-md">
                <h3 className="text-red-400 font-medium mb-2">Error Loading Profile</h3>
                <p className="text-neutral-300 mb-4">{error}</p>
                {error.includes('Database is currently unavailable') ? (
                  <div className="text-neutral-400 text-sm mb-4">
                    <p>The database connection is currently unavailable. This could be due to maintenance or server issues.</p>
                    <p className="mt-2">Please try again later or contact support if the issue persists.</p>
                  </div>
                ) : null}
                <button
                  onClick={() => window.location.reload()}
                  className="px-4 py-2 bg-red-900/50 hover:bg-red-800/50 text-white rounded-md transition-colors"
                >
                  Retry
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-8">

                {/* Render components based on database only */}
                {profileData && profileData.components && profileData.components.length > 0 ? (
                  // Map through components from database in order
                  profileData.components
                    .sort((a: any, b: any) => parseInt(a.order) - parseInt(b.order))
                    .map((component: any) => {
                      // Skip old component types
                      if (component.componentType === 'banner' || component.componentType === 'profilePicture') {
                        return null;
                      } else if (component.componentType === 'socialLinks') {
                        const details = component.details || {};
                        return (

                          <EditUserDetails
                            key="socialLinks"
                            address={address as string}
                            initialData={{
                              socialLinks: details.socialLinks || {}
                            }}
                            hidden={component.hidden}
                          />
                        );
                      } else if (component.componentType === 'hero') {
                        const details = component.details || {};
                        return (

                          <EditHero
                            key="hero"
                            address={address as string}
                            initialContent={details.heroContent || []}
                            hidden={component.hidden}
                          />
                        );
                      } else if (component.componentType === 'bannerpfp') {
                        return (

                          <EditBannerPfp
                            key="bannerpfp"
                            bannerPfpState={{...bannerPfpState, profileNameStyle: {...bannerPfpState.profileNameStyle, effect: (bannerPfpState.profileNameStyle.effect as 'typewriter' | 'none')}}}
                            setBannerPfpState={(state) => setBannerPfpState(state as any)}
                            address={address as string}
                            hidden={component.hidden}
                          />
                        );
                      }
                      return null;
                    })
                ) : (
                  // No components found in database
                  <div className="flex justify-center items-center min-h-[200px]">
                    <div className="flex flex-col items-center gap-4">
                      <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                      <p className="text-neutral-400">Creating your profile components...</p>
                      <p className="text-neutral-500 text-sm mb-4">This may take a moment.</p>
                      <button
                        onClick={() => window.location.reload()}
                        className="px-4 py-2 bg-blue-900/50 hover:bg-blue-800/50 text-white rounded-md transition-colors text-sm"
                      >
                        Refresh Page
                      </button>
                    </div>
                  </div>
                )}
            </div>
          )}
        </WalletValidator>
      </div>
    </main>
  );
}
