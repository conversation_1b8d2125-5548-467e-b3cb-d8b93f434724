'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { Heart, Star } from 'lucide-react';
import ScrollVelocity from '@/components/ui/ScrollVelocity';
import DiscoverProfileImageStatusChecker from '@/components/DiscoverProfileImageStatusChecker';
import DiscoverProfileNameStatusChecker from '@/components/DiscoverProfileNameStatusChecker';

// Function to format numbers with K, M, B, T suffixes
const formatNumber = (num: number): string => {
  if (num < 1000) return num.toString();

  const units = ['', 'K', 'M', 'B', 'T'];
  const unitIndex = Math.floor(Math.log10(num) / 3);
  const value = num / Math.pow(1000, unitIndex);

  // Format with one decimal place if needed
  return value.toFixed(value % 1 === 0 ? 0 : 1) + units[unitIndex];
};

interface Profile {
  address: string;
  chain: string;
  name: string; // Keep for backward compatibility
  urlName?: string; // URL-friendly name for navigation
  displayName?: string; // Display name for showing to users
  role?: string;
  createdAt: string;
  updatedAt: string;
}

interface ProfileCardProps {
  profile: Profile;
  currentUserAddress?: string;
  isFeatured?: boolean;
}

export default function ProfileCard({ profile, currentUserAddress, isFeatured = false }: ProfileCardProps) {
  const [profilePicUrl, setProfilePicUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [totalLikes, setTotalLikes] = useState(0);
  const [hasLiked, setHasLiked] = useState(false);
  const [isLikeLoading, setIsLikeLoading] = useState(false);

  // Load profile picture and like status
  useEffect(() => {
    const loadProfileData = async () => {
      try {
        setIsLoading(true);

        // Fetch profile picture from bannerpfp component
        const response = await fetch(`/api/bannerpfp/${profile.address}`);

        if (response.ok) {
          const data = await response.json();
          setProfilePicUrl(data.profileUrl);
        }

        // Fetch like status if user is connected
        if (currentUserAddress) {
          const likeResponse = await fetch(`/api/likes?address=${profile.address}&likerAddress=${currentUserAddress}`);

          if (likeResponse.ok) {
            const likeData = await likeResponse.json();
            setTotalLikes(likeData.totalLikes);
            setHasLiked(likeData.hasLiked);
          }
        }
      } catch (error) {
        console.error('Error loading profile data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadProfileData();
  }, [profile.address, currentUserAddress]);

  // Handle like/unlike
  const handleLikeToggle = async () => {
    if (!currentUserAddress) {
      console.log('Wallet not connected, cannot like profile');
      return;
    }

    try {
      setIsLikeLoading(true);

      console.log('Sending like request with:', {
        likerAddress: currentUserAddress,
        likedAddress: profile.address,
      });

      const response = await fetch('/api/likes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          likerAddress: currentUserAddress,
          likedAddress: profile.address,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Like API error response:', errorText);
        throw new Error(`Failed to toggle like: ${errorText}`);
      }

      const data = await response.json();
      console.log('Like API success response:', data);

      setTotalLikes(data.totalLikes);
      setHasLiked(data.hasLiked);
    } catch (error) {
      console.error('Error toggling like:', error);
    } finally {
      setIsLikeLoading(false);
    }
  };

  // Display name (use displayName, then name, or fallback to first 8 chars of address)
  const displayName = profile.displayName || profile.name || profile.address.substring(0, 8);

  // URL name for navigation (use urlName, then name, or fallback to address)
  const urlName = profile.urlName || profile.name || profile.address;

  return (
    <div className="relative bg-transparent rounded-lg overflow-hidden shadow-sm border border-neutral-800/20">

      {/* Card Content */}
      <div className="relative z-10 flex flex-col h-full bg-transparent p-[1px]">
        <DiscoverProfileImageStatusChecker profileAddress={profile.address} profileName={urlName}>
          <Link href={`/${urlName}`} className="block">
            <div className="relative w-full overflow-hidden bg-transparent" style={{ height: '160px', position: 'relative' }}>
            {/* Like Button - Top Right */}
            <button
              onClick={(e) => {
                e.preventDefault(); // Prevent navigation when clicking the heart
                e.stopPropagation(); // Stop event propagation
                handleLikeToggle();
              }}
              disabled={isLikeLoading || !currentUserAddress}
              className={`absolute top-2 right-2 z-10 flex items-center justify-center p-1.5 rounded-full transition-colors bg-black/30 backdrop-blur-sm ${
                hasLiked
                  ? 'text-red-500 hover:text-red-400'
                  : 'text-white hover:text-red-400'
              } ${!currentUserAddress ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
              aria-label={hasLiked ? 'Unlike profile' : 'Like profile'}
            >
              <Heart
                className={`h-4 w-4 ${hasLiked ? 'fill-current' : ''} ${isLikeLoading ? 'animate-pulse' : ''}`}
              />
              <span className="sr-only">{totalLikes} likes</span>
            </button>

            {isLoading ? (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
              </div>
            ) : profilePicUrl ? (
              <img
                src={profilePicUrl}
                alt={displayName}
                className="w-full h-full object-cover object-center transition-transform duration-500 hover:scale-105"
                loading="lazy"
                width="100"
                height="160"
              />
            ) : (
              <div className="absolute inset-0 flex items-center justify-center bg-transparent border border-neutral-800">
                <span className="text-xl font-bold text-white/70">{displayName.charAt(0).toUpperCase()}</span>
              </div>
            )}
            </div>
          </Link>
        </DiscoverProfileImageStatusChecker>

        <div className="p-1.5 bg-transparent">
          <DiscoverProfileNameStatusChecker profileAddress={profile.address} profileName={urlName}>
            <Link href={`/${urlName}`} className="block w-full">
            <div className="flex flex-col items-center">
              <div className="w-full overflow-hidden mb-0.5">
                <ScrollVelocity
                  texts={[displayName, displayName]}
                  velocity={15}
                  className="font-medium text-sm text-white hover:text-blue-400"
                  numCopies={10}
                  parallaxClassName="parallax-name"
                  scrollerClassName="scroller-name"
                  parallaxStyle={{ height: '24px' }}
                />
              </div>
              <span className="text-xs text-neutral-400">{formatNumber(totalLikes)} likes</span>
            </div>
            </Link>
          </DiscoverProfileNameStatusChecker>
        </div>
      </div>
    </div>
  );
}
