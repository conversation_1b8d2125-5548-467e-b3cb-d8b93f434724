'use server';

import { readFileSync } from 'fs';
import { join } from 'path';
import { db } from '@/db/drizzle';
import { web3Profile, componentPositions } from '@/db/schema';
import { eq, and } from 'drizzle-orm';
import { getComponentDefaults, getProfileDefaults } from '@/app/utils/systemSettings';
import { saveImage, getComponentImages } from '@/lib/imageStorage';

/**
 * Get default image from public folder as base64
 * @param filename Image filename in public folder
 * @returns Base64 encoded image string
 */
async function getDefaultImageAsBase64(filename: string): Promise<string> {
  const filepath = join(process.cwd(), 'public', filename);
  const buffer = readFileSync(filepath);
  return buffer.toString('base64');
}

/**
 * Creates a profile and default component positions for a new user
 * @param address Wallet address
 * @param chain Blockchain network chain ID
 */
export async function createProfile(address: string, chain: string = "25"): Promise<void> {
  if (!address) return;

  try {


    // Check if profile exists
    const existingProfile = await db
      .select()
      .from(web3Profile)
      .where(eq(web3Profile.address, address));

    // If profile doesn't exist, create it with default values
    if (existingProfile.length === 0) {


      // Get profile defaults from system settings
      const profileDefaults = await getProfileDefaults(chain);

      // Calculate expiry date if default_expiry_days is set
      let expiryDate = null;
      if (profileDefaults.default_expiry_days) {
        const days = parseInt(profileDefaults.default_expiry_days);
        if (!isNaN(days) && days > 0) {
          expiryDate = new Date();
          expiryDate.setDate(expiryDate.getDate() + days);
        }
      }

      // Format the default profile name using the template from settings
      const defaultNameFormat = profileDefaults.default_profile_name_format;
      const defaultName = defaultNameFormat.replace('{address}', address.substring(0, 8));

      // Create a URL-friendly name (single word) by removing spaces and special characters
      const urlName = defaultName.replace(/\s+/g, '').replace(/[^a-zA-Z0-9]/g, '');

      // Create profile with values from database defaults
      await db.insert(web3Profile).values({
        address,
        chain,
        name: urlName, // Default URL name (single word) derived from settings
        role: profileDefaults.default_role, // Default role from settings
        status: profileDefaults.default_status, // Default status from settings
        expiryDate, // Expiry date calculated from settings
        transactionHash: null, // No transaction hash by default
      });


    } else {

    }

    // Check if components exist for this address
    const existingComponents = await db
      .select()
      .from(componentPositions)
      .where(eq(componentPositions.address, address));



    // If no components exist, create default ones
    if (existingComponents.length === 0) {

      const now = new Date();

      // Create default components with images where applicable
      try {


        // Get default images
        const defaultBannerBase64 = await getDefaultImageAsBase64('banner.png');
        const defaultProfileBase64 = await getDefaultImageAsBase64('pfp.jpg');

        // Get component defaults from system settings
        const componentDefaults = await getComponentDefaults(chain);
        // Get profile defaults for name and bio
        const profileDefaults = await getProfileDefaults(chain);

        // Create components based on defaults
        for (const defaultComponent of componentDefaults) {
          // Format the default profile name using the template from settings
          const defaultNameFormat = profileDefaults.default_profile_name_format || 'Web3 User {address}';
          const formattedName = defaultNameFormat.replace('{address}', address.substring(0, 8));

          // Get default bio from settings or use empty string
          const defaultBio = profileDefaults.default_profile_bio || '';

          // Create component with default values
          const component: any = {
            address,
            chain,
            componentType: defaultComponent.componentType,
            order: defaultComponent.order,
            hidden: defaultComponent.hidden,
            createdAt: now,
            updatedAt: now,
            details: {
              backgroundColor: 'transparent',
              fontColor: null
            }
          };

          // Skip deprecated components
          if (defaultComponent.componentType === 'banner' || defaultComponent.componentType === 'profilePicture') {

            continue;
          }

          // Add component-specific properties to details field
          if (defaultComponent.componentType === 'hero') {
            // Get heroContent from system settings if available, otherwise use default
            const defaultHeroContent = defaultComponent.details?.heroContent || [
              {
                title: "My First Section",
                description: "This is my first section. Click edit to change this text.",
                contentType: 'color',
                colorGradient: 'linear-gradient(to bottom right, var(--cyan-500), var(--emerald-500))'
              }
            ];

            component.details = {
              ...component.details,
              heroContent: defaultHeroContent
            };
          } else if (defaultComponent.componentType === 'bannerpfp') {
            // Get profileNameStyle from system settings if available, otherwise use default
            const defaultProfileNameStyle = defaultComponent.details?.profileNameStyle || {
              fontSize: '1.5rem',
              fontWeight: 'bold',
              fontColor: '#ffffff',
              effect: 'typewriter'
            };

            component.details = {
              ...component.details,
              profileShape: defaultComponent.details?.profileShape || 'circular',
              profileHorizontalPosition: defaultComponent.details?.profileHorizontalPosition || 50,
              profileNameHorizontalPosition: defaultComponent.details?.profileNameHorizontalPosition || 50,
              profileNameStyle: defaultProfileNameStyle,
              profileName: formattedName,
              profileBio: defaultBio
            };
          } else if (defaultComponent.componentType === 'socialLinks') {
            // Get socialLinks from system settings if available, otherwise use empty defaults
            const defaultSocialLinks = defaultComponent.details?.socialLinks || {
              twitter: '',
              discord: '',
              telegram: '',
              website: '',
              facebook: '',
              youtube: '',
              email: '',
              linkedin: '',
              cro: ''
            };

            component.details = {
              ...component.details,
              socialLinks: defaultSocialLinks
            };
          }

          // Insert the component
          await db.insert(componentPositions).values(component);

        }



        // Save default images for the bannerpfp component
        // This ensures images are available immediately without needing to generate them on demand
        try {


          // Save the banner image
          await saveImage(address, 'bannerpfp', 'banner', defaultBannerBase64, {
            scale: 1,
            positionX: 0,
            positionY: 0
          });

          // Save the profile image
          await saveImage(address, 'bannerpfp', 'profile', defaultProfileBase64, {
            scale: 1,
            positionX: 0,
            positionY: 0
          });


        } catch (imageError) {

        }
      } catch (error) {

      }


    } else {
      // Check if all required components exist
      // Get component defaults from system settings
      const componentDefaults = await getComponentDefaults(chain);
      // Filter out deprecated components
      const requiredComponents = componentDefaults
        .filter(c => c.componentType !== 'banner' && c.componentType !== 'profilePicture')
        .map(c => c.componentType);
      const existingComponentTypes = existingComponents.map(c => c.componentType);

      for (const requiredType of requiredComponents) {
        if (!existingComponentTypes.includes(requiredType)) {


          // Find the highest order value
          const maxOrder = existingComponents.reduce((max, component) => {
            const order = parseInt(component.order);
            return order > max ? order : max;
          }, 0);

          // Get profile defaults for name and bio
          const profileDefaults = await getProfileDefaults(chain);
          const defaultNameFormat = profileDefaults.default_profile_name_format || 'Web3 User {address}';
          const formattedName = defaultNameFormat.replace('{address}', address.substring(0, 8));
          const defaultBio = profileDefaults.default_profile_bio || '';

          // Create the missing component with the next order value and appropriate details
          const component: any = {
            address,
            chain,
            componentType: requiredType,
            order: (maxOrder + 1).toString(),
            hidden: 'N',
            details: {
              backgroundColor: 'transparent',
              fontColor: null
            },
            createdAt: new Date(),
            updatedAt: new Date()
          };

          // Skip deprecated components (should never get here due to filtering above, but just in case)
          if (requiredType === 'banner' || requiredType === 'profilePicture') {

            continue;
          }

          // Add component-specific details
          if (requiredType === 'hero') {
            // Get component defaults from system settings
            const componentDefaults = await getComponentDefaults(chain);
            const heroDefaults = componentDefaults.find(comp => comp.componentType === 'hero');

            // Get heroContent from system settings if available, otherwise use default
            const defaultHeroContent = heroDefaults?.details?.heroContent || [
              {
                title: "My First Section",
                description: "This is my first section. Click edit to change this text.",
                contentType: 'color',
                colorGradient: 'linear-gradient(to bottom right, var(--cyan-500), var(--emerald-500))'
              }
            ];

            component.details = {
              ...component.details,
              heroContent: defaultHeroContent
            };
          } else if (requiredType === 'bannerpfp') {
            // Get component defaults from system settings
            const componentDefaults = await getComponentDefaults(chain);
            const bannerpfpDefaults = componentDefaults.find(comp => comp.componentType === 'bannerpfp');

            // Get profileNameStyle from system settings if available, otherwise use default
            const defaultProfileNameStyle = bannerpfpDefaults?.details?.profileNameStyle || {
              fontSize: '1.5rem',
              fontWeight: 'bold',
              fontColor: '#ffffff',
              effect: 'typewriter'
            };

            component.details = {
              ...component.details,
              profileShape: bannerpfpDefaults?.details?.profileShape || 'circular',
              profileHorizontalPosition: bannerpfpDefaults?.details?.profileHorizontalPosition || 50,
              profileNameHorizontalPosition: bannerpfpDefaults?.details?.profileNameHorizontalPosition || 50,
              profileNameStyle: defaultProfileNameStyle,
              profileName: formattedName,
              profileBio: defaultBio
            };
          } else if (requiredType === 'socialLinks') {
            // Get component defaults from system settings
            const componentDefaults = await getComponentDefaults(chain);
            const socialLinksDefaults = componentDefaults.find(comp => comp.componentType === 'socialLinks');

            // Get socialLinks from system settings if available, otherwise use empty defaults
            const defaultSocialLinks = socialLinksDefaults?.details?.socialLinks || {
              twitter: '',
              discord: '',
              telegram: '',
              website: '',
              facebook: '',
              youtube: '',
              email: '',
              linkedin: '',
              cro: ''
            };

            component.details = {
              ...component.details,
              socialLinks: defaultSocialLinks
            };
          }

          await db.insert(componentPositions).values(component);


          // If the component is bannerpfp, save default images
          if (requiredType === 'bannerpfp') {
            try {

              const defaultBannerBase64 = await getDefaultImageAsBase64('banner.png');
              const defaultProfileBase64 = await getDefaultImageAsBase64('pfp.jpg');

              // Save the banner image
              await saveImage(address, 'bannerpfp', 'banner', defaultBannerBase64, {
                scale: 1,
                positionX: 0,
                positionY: 0
              });

              // Save the profile image
              await saveImage(address, 'bannerpfp', 'profile', defaultProfileBase64, {
                scale: 1,
                positionX: 0,
                positionY: 0
              });


            } catch (imageError) {

            }
          }
        }
      }

      // Banner and profilePicture components are deprecated, using bannerpfp instead

      // Check if bannerpfp component exists
      const existingBannerpfp = await db
        .select()
        .from(componentPositions)
        .where(
          and(
            eq(componentPositions.address, address),
            eq(componentPositions.componentType, 'bannerpfp')
          )
        );

      // If bannerpfp component doesn't exist, set default
      if (existingBannerpfp.length === 0) {
        try {

          const now = new Date();
          const defaultBannerBase64 = await getDefaultImageAsBase64('banner.png');
          const defaultProfileBase64 = await getDefaultImageAsBase64('pfp.jpg');

          // Get profile defaults for name and bio
          const profileDefaults = await getProfileDefaults(chain);
          const defaultNameFormat = profileDefaults.default_profile_name_format || 'Web3 User {address}';
          const formattedName = defaultNameFormat.replace('{address}', address.substring(0, 8));
          const defaultBio = profileDefaults.default_profile_bio || '';

          // Insert new record with details field
          await db.insert(componentPositions).values({
            address,
            chain,
            componentType: 'bannerpfp',
            order: '5',
            hidden: 'N',
            details: {
              backgroundColor: 'transparent',
              fontColor: null,
              profileShape: 'circular',
              profileHorizontalPosition: 50,
              profileNameHorizontalPosition: 50,
              profileNameStyle: {
                fontSize: '1.5rem',
                fontWeight: 'bold',
                fontColor: '#ffffff',
                effect: 'typewriter'
              },
              profileName: formattedName,
              profileBio: defaultBio
            },
            createdAt: now,
            updatedAt: now
          });

          // Save the banner image to componentImages table
          await saveImage(address, 'bannerpfp', 'banner', defaultBannerBase64, {
            scale: 1,
            positionX: 0,
            positionY: 0
          });

          // Save the profile image to componentImages table
          await saveImage(address, 'bannerpfp', 'profile', defaultProfileBase64, {
            scale: 1,
            positionX: 0,
            positionY: 0
          });


        } catch (error) {

        }
      }
    }
  } catch (error) {
    console.error('Error creating profile:', error);
  }
}
