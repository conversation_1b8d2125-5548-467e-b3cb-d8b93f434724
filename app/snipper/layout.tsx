import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "../globals.css";
import { ThemeProvider } from "@/components/ThemeProvider";
import { Toaster } from "sonner";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Snipper Dashboard - Web3Socials",
  description: "Wallet transaction monitoring dashboard for Web3Socials",
};

export default function SnipperLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className="dark">
      <ThemeProvider attribute="class" defaultTheme="dark" enableSystem={false} forcedTheme="dark">
        <main className="min-h-screen bg-background dark">
          {children}
        </main>
        <Toaster position="top-right" theme="dark" />
      </ThemeProvider>
    </div>
  );
}
