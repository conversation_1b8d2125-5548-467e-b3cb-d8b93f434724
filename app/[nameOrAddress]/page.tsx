import ClientPage from './ClientPage';

export default async function Page({
  params,
}: {
  params?: Promise<{ nameOrAddress: string }>
}) {
  // Await the params if they are provided as a Promise
  const resolvedParams = params ? await params : { nameOrAddress: '' };

  // Ensure nameOrAddress is never undefined
  const nameOrAddress = resolvedParams.nameOrAddress || '';
  // Page component received nameOrAddress parameter

  return <ClientPage nameOrAddress={nameOrAddress} />;
}



