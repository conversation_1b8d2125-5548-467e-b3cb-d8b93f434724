'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { useAppKitAccount, useAppKitNetwork } from '@reown/appkit/react';
import Link from 'next/link';
import Image from 'next/image';
import { AlertTriangle, Clock, X, ExternalLink, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import SimpleWalletValidator from '@/components/SimpleWalletValidator';
import {
  hasTokenRequirements,
  hasAnyTokenRequirements,
  isReferralOnlyMode,
  getValidationMode,
  getValidationModeComprehensive,
  checkWalletBalance,
  checkWalletBalanceWithRequirements,
  FullTokenRequirements
} from '@/lib/tokenValidation';

export default function ProfileErrorPage() {
  const searchParams = useSearchParams();
  const status = searchParams?.get('status') || 'unknown';
  const profileName = searchParams?.get('name') || '';
  const { isConnected, address } = useAppKitAccount();
  const { chainId } = useAppKitNetwork();
  const [transactionHash, setTransactionHash] = useState('');
  const [referralCode, setReferralCode] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [isOwner, setIsOwner] = useState(false);
  const [isCheckingOwnership, setIsCheckingOwnership] = useState(false);
  const [userReferralCode, setUserReferralCode] = useState<string | null>(null);
  const [tokenRequirements, setTokenRequirements] = useState<{
    tokenAddress: string;
    tokenName: string;
    minimumHoldings: string;
    secondaryMinHoldings?: string;
  } | null>(null);
  const [fullTokenRequirements, setFullTokenRequirements] = useState<FullTokenRequirements | null>(null);
  const [isLoadingRequirements, setIsLoadingRequirements] = useState(true);
  const [walletBalance, setWalletBalance] = useState<{
    canProceed: boolean;
    validationPath: 'main' | 'secondary' | 'none';
    mainTokenResult?: {
      hasEnoughTokens: boolean;
      currentBalance: string;
      minimumRequired: string;
      error?: string;
    };
    secondaryTokenResult?: {
      hasEnoughTokens: boolean;
      currentBalance: string;
      minimumRequired: string;
      tokenName: string;
      error?: string;
    };
    secondaryMinHoldingsResult?: {
      hasEnoughTokens: boolean;
      currentBalance: string;
      minimumRequired: string;
      error?: string;
    };
    error?: string;
  } | null>(null);
  const [isCheckingBalance, setIsCheckingBalance] = useState(false);

  // Fetch token requirements
  useEffect(() => {
    async function getTokenRequirements() {
      // Wait for chainId to be available
      if (!chainId) {
        console.log('Waiting for chainId to be available...');
        setIsLoadingRequirements(true);
        return;
      }

      try {
        setIsLoadingRequirements(true);
        console.log(`Fetching token requirements for chain: ${chainId}`);

        // Fetch full requirements from the token requirements API
        const response = await fetch(`/api/token-requirements/${chainId}?full=true`);
        const fullRequirements = await response.json();

        console.log('=== TOKEN REQUIREMENTS DEBUG ===');
        console.log('Full Requirements:', JSON.stringify(fullRequirements, null, 2));
        console.log('Chain ID:', chainId);

        // Convert full requirements to legacy format for display compatibility
        const legacyRequirements = {
          tokenAddress: fullRequirements.mainRequirement?.tokenAddress || '',
          tokenName: fullRequirements.mainRequirement?.tokenName || 'Web3Tools',
          minimumHoldings: fullRequirements.mainRequirement?.minimumHoldings || '0',
          secondaryMinHoldings: fullRequirements.mainRequirement?.secondaryMinHoldings || '0'
        };

        setTokenRequirements(legacyRequirements);
        setFullTokenRequirements(fullRequirements);
      } catch (error) {
        console.error('Error fetching token requirements:', error);
        // Set default empty requirements on error
        setTokenRequirements({
          tokenAddress: '',
          tokenName: 'Web3Tools',
          minimumHoldings: '0',
          secondaryMinHoldings: '0'
        });
        const defaultFullRequirements = {
          mainRequirement: {
            chainId: chainId.toString(),
            chainName: '',
            tokenAddress: '',
            tokenName: 'Web3Tools',
            minimumHoldings: '0',
            secondaryMinHoldings: '0'
          },
          secondaryRequirements: []
        };
        setFullTokenRequirements(defaultFullRequirements);
      } finally {
        setIsLoadingRequirements(false);
      }
    }

    getTokenRequirements();
  }, [chainId]);

  // Check wallet balance when token requirements are loaded and wallet is connected
  useEffect(() => {
    async function checkBalance() {
      if (!fullTokenRequirements || !tokenRequirements || !isConnected || !address || !chainId) {
        setWalletBalance(null);
        return;
      }

      // Only check balance if token requirements are configured (check both legacy and full requirements)
      if (!hasAnyTokenRequirements(tokenRequirements || undefined, fullTokenRequirements || undefined)) {
        setWalletBalance(null);
        return;
      }

      try {
        setIsCheckingBalance(true);

        const balanceResult = await checkWalletBalanceWithRequirements(
          address,
          fullTokenRequirements,
          chainId.toString()
        );
        setWalletBalance(balanceResult);
      } catch (error) {
        console.error('Error checking wallet balance:', error);
        setWalletBalance({
          canProceed: false,
          validationPath: 'none',
          error: 'Failed to check wallet balance'
        });
      } finally {
        setIsCheckingBalance(false);
      }
    }

    checkBalance();
  }, [fullTokenRequirements, tokenRequirements, isConnected, address, chainId]);

  // Check if the connected wallet is the profile owner
  useEffect(() => {
    async function checkOwnership() {
      if (!isConnected || !address || !profileName) {
        setIsOwner(false);
        return;
      }

      try {
        setIsCheckingOwnership(true);
        console.log(`Checking ownership for profile: ${profileName} with wallet: ${address}`);
        const response = await fetch(`/api/profile/check-ownership?name=${profileName}&walletAddress=${address}`);
        const data = await response.json();

        console.log('Ownership check result:', data);
        setIsOwner(data.isOwner);
      } catch (error) {
        console.error('Error checking ownership:', error);
        setIsOwner(false);
      } finally {
        setIsCheckingOwnership(false);
      }
    }

    checkOwnership();

    // Fetch referral code for pending users to enable Twitter sharing
    if (status === 'pending' && address) {
      fetchUserReferralCode();
    }
  }, [address, profileName, isConnected, status]);

  // Function to get status icon
  const getStatusIcon = () => {
    switch (status) {
      case 'new':
        return <Clock className="h-12 w-12 text-blue-500" />;
      case 'in-progress':
        return <Clock className="h-12 w-12 text-yellow-500" />;
      case 'pending':
        return <AlertTriangle className="h-12 w-12 text-orange-500" />;
      case 'deleted':
        return <X className="h-12 w-12 text-red-500" />;
      case 'expired':
        return <Clock className="h-12 w-12 text-blue-500" />;
      default:
        return <AlertTriangle className="h-12 w-12 text-red-500" />;
    }
  };

  // Function to get status title
  const getStatusTitle = () => {
    switch (status) {
      case 'new':
        return 'Action Needed';
      case 'in-progress':
        return 'Profile In Progress';
      case 'pending':
        return 'Profile Pending Approval';
      case 'deleted':
        return 'Profile Deleted';
      case 'expired':
        return 'Profile Expired - Action Needed';
      default:
        return 'Profile Not Available';
    }
  };

  // Function to get status message
  const getStatusMessage = () => {
    const validationMode = getValidationModeComprehensive(tokenRequirements || undefined, fullTokenRequirements || undefined);
    console.log('=== VALIDATION MODE DEBUG ===');
    console.log('Validation Mode:', validationMode);
    console.log('hasAnyTokenRequirements result:', hasAnyTokenRequirements(tokenRequirements || undefined, fullTokenRequirements || undefined));

    switch (status) {
      case 'new':
        if (validationMode === 'token') {
          return (
            <>
              Hi, welcome to Web3Socials!
            </>
          );
        } else {
          return (
            <>
              Hi, welcome to Web3Socials! This is a community project and in order for you to view your own webpage (https://Web3Socials.fun/{profileName || 'name'}), please submit a referral code and wait for admin approval. For more inquiries, contact us on X.
            </>
          );
        }
      case 'in-progress':
        return (
          <>
            Hi there! Please be patient as your transaction is being processed. If you have any questions, please DM us on X at https://x.com/Web3Tools_fun.
          </>
        );
      case 'pending':
        return (
          <div className="space-y-4">
            <p>Hi there! Please be patient as your transaction is being reviewed. If you have any questions, please DM us on X at https://x.com/Web3Tools_fun.</p>

            {/* Twitter Share Section for Pending Users */}
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-4">
              <div className="text-center space-y-3">
                <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                  While you wait, share your Web3 profile with friends! 🚀
                </p>
                <button
                  onClick={() => window.open(generateTwitterPostUrl(), '_blank')}
                  className="inline-flex items-center gap-2 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 font-medium"
                >
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                  </svg>
                  Share on Twitter
                </button>

                {userReferralCode && (
                  <div className="text-xs text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/30 rounded-md p-2">
                    <p>Your referral code <span className="font-mono font-bold">{userReferralCode}</span> will be included in the post!</p>
                  </div>
                )}

                <div className="text-xs text-blue-600 dark:text-blue-400">
                  <p>This will open Twitter with a pre-filled post including your profile link{userReferralCode ? ' and referral code' : ''}.</p>
                </div>
              </div>
            </div>
          </div>
        );
      case 'deleted':
        return (
          <>
            This profile has been deleted. Please contact an admin for assistance.
          </>
        );
      case 'expired':
        if (validationMode === 'token') {
          return (
            <>
              Hi, welcome back to Web3Socials! Your profile has expired. Connect your wallet to check if you qualify for renewal.
            </>
          );
        } else {
          return (
            <>
              Hi, welcome back to Web3Socials! Your profile has expired. In order for you to view your webpage (https://Web3Socials.fun/{profileName || 'name'}), please submit a referral code and wait for admin approval. For more inquiries, contact us on X.
            </>
          );
        }
      default:
        return (
          <>
            This profile is not available. Please check the address or name and try again.
          </>
        );
    }
  };

  // Function to submit transaction hash or referral code
  const submitTransactionHash = async () => {
    // Reset error message
    setErrorMessage(null);

    // Check if the connected wallet is the profile owner
    if (!isOwner) {
      setErrorMessage('You can only submit information for your own profile.');
      return;
    }

    // Get validation mode
    const validationMode = getValidationModeComprehensive(tokenRequirements || undefined, fullTokenRequirements || undefined);

    // Validate based on mode
    if (validationMode === 'token') {
      // Token mode - no validation needed since we removed transaction hash requirement
    } else {
      // Referral-only mode - referral code is optional
      // No validation needed - user can submit with or without referral code
    }

    try {
      setIsSubmitting(true);

      const response = await fetch('/api/profile/submit-transaction', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          address, // Use the connected wallet address
          transactionHash: validationMode === 'token' ? transactionHash : undefined,
          referralCode: referralCode || undefined,
          validationMode, // Include validation mode for backend processing
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to submit transaction hash');
      }

      setSubmitSuccess(true);
      setTransactionHash('');
      setReferralCode('');

      // Fetch user's referral code for Twitter sharing
      fetchUserReferralCode();
    } catch (error) {
      // Error submitting transaction hash
      setErrorMessage(error instanceof Error ? error.message : 'Failed to submit transaction hash. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Function to fetch user's referral code
  const fetchUserReferralCode = async () => {
    if (!address) return;

    try {
      const response = await fetch(`/api/referrals/stats?address=${address}`);
      if (response.ok) {
        const data = await response.json();
        setUserReferralCode(data.referralCode || null);
      }
    } catch (error) {
      console.error('Error fetching referral code:', error);
    }
  };

  // Function to generate Twitter post URL
  const generateTwitterPostUrl = () => {
    const profileUrl = `https://web3socials.fun/${profileName || 'name'}`;

    let tweetText = `🎉 Just created my Web3 profile on Web3Socials!

Check out my profile: ${profileUrl}`;

    if (userReferralCode) {
      tweetText += `

🎁 Use my referral code: ${userReferralCode}`;
    }

    tweetText += `

Build your own Web3 profile in minutes! 🚀

@Web3Tools_fun #Web3Socials #Web3 #Blockchain #CronosChain #Web3Profile`;

    const encodedText = encodeURIComponent(tweetText);
    return `https://twitter.com/intent/tweet?text=${encodedText}`;
  };

  return (
    <div className="container max-w-4xl mx-auto py-12 px-4">
      <Link href="/discover" className="inline-flex items-center text-blue-500 hover:text-blue-700 mb-8">
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back to Discover
      </Link>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
        <div className="flex flex-col items-center text-center mb-8">
          {status === 'new' || status === 'in-progress' || status === 'pending' ? (
            <div className="relative w-24 h-24 rounded-full overflow-hidden mb-4">
              <Image
                src="/pfp.jpg"
                alt="Web3Tools Profile"
                width={96}
                height={96}
                className="object-cover"
              />
            </div>
          ) : (
            getStatusIcon()
          )}
          <h1 className="text-2xl font-bold mt-4">{getStatusTitle()}</h1>
          <div className="text-gray-600 dark:text-gray-300 mt-2 max-w-2xl">
            {isLoadingRequirements && status === 'new' ? 'Loading token requirements...' : getStatusMessage()}
          </div>
          {(status === 'new' || status === 'in-progress' || status === 'pending') && (
            <a
              href="https://x.com/Web3Tools_fun"
              target="_blank"
              rel="noopener noreferrer"
              className="mt-4 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              Contact us on X
            </a>
          )}
        </div>

        {(status === 'new' || status === 'expired') && (
          <SimpleWalletValidator
            fallbackContent={
              <div className="mt-8 max-w-md mx-auto">
                <div className="bg-yellow-100 dark:bg-yellow-900/30 border border-yellow-300 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200 rounded-md p-4 mb-4">
                  Please connect your wallet to verify your profile.
                </div>
              </div>
            }
          >
            <div className="mt-8 max-w-md mx-auto">
              {!profileName ? (
                <div className="bg-yellow-100 dark:bg-yellow-900/30 border border-yellow-300 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200 rounded-md p-4 mb-4">
                  Profile name information is missing. Please try accessing this page from the discover page.
                </div>
              ) : isCheckingOwnership ? (
                <div className="bg-blue-100 dark:bg-blue-900/30 border border-blue-300 dark:border-blue-800 text-blue-800 dark:text-blue-200 rounded-md p-4 mb-4">
                  Checking profile ownership...
                </div>
              ) : !isOwner ? (
                <div className="bg-yellow-100 dark:bg-yellow-900/30 border border-yellow-300 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200 rounded-md p-4 mb-4">
                  <p className="mb-2">You can only submit a transaction hash for your own profile. Please connect with the wallet that owns this profile.</p>
                  <p className="text-xs">Profile: {profileName}</p>
                  <p className="text-xs">Connected wallet: {address ? `${address.substring(0, 6)}...${address.substring(address.length - 4)}` : 'Not connected'}</p>
                  <p className="text-xs mt-2 italic">Note: If this is your profile but you're seeing this error, please make sure you're connected to the correct wallet that was used to create this profile.</p>
                </div>
              ) : submitSuccess ? (

              <div className="bg-green-100 dark:bg-green-900/30 border border-green-300 dark:border-green-800 text-green-800 dark:text-green-200 rounded-md p-4 mb-4">
                <div className="text-center">
                  <p className="mb-4">
                    {getValidationModeComprehensive(tokenRequirements || undefined, fullTokenRequirements || undefined) === 'token'
                      ? '🎉 Transaction hash submitted successfully! Your profile will be reviewed soon.'
                      : '🎉 Referral code submitted successfully! Your profile will be reviewed for approval.'
                    }
                  </p>

                  {/* Twitter Share Button */}
                  <div className="space-y-3">
                    <p className="text-sm font-medium">Share your Web3 profile with friends!</p>
                    <button
                      onClick={() => window.open(generateTwitterPostUrl(), '_blank')}
                      className="inline-flex items-center gap-2 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 font-medium"
                    >
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                      </svg>
                      Share on Twitter
                    </button>

                    {userReferralCode && (
                      <div className="text-xs text-green-700 dark:text-green-300 bg-green-50 dark:bg-green-900/20 rounded-md p-2 mt-2">
                        <p>Your referral code <span className="font-mono font-bold">{userReferralCode}</span> will be included in the post!</p>
                      </div>
                    )}

                    <div className="text-xs text-gray-600 dark:text-gray-400 mt-2">
                      <p>This will open Twitter with a pre-filled post including your profile link{userReferralCode ? ' and referral code' : ''}.</p>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <>
                {getValidationModeComprehensive(tokenRequirements || undefined, fullTokenRequirements || undefined) === 'token' ? (
                  // Token validation mode - show wallet balance check and transaction hash form
                  <>
                    {/* Wallet Balance Check */}
                    {isCheckingBalance ? (
                      <div className="bg-blue-100 dark:bg-blue-900/30 border border-blue-300 dark:border-blue-800 text-blue-800 dark:text-blue-200 rounded-md p-4 mb-4">
                        Checking your wallet balance...
                      </div>
                    ) : walletBalance ? (
                      <div className={`border rounded-md p-4 mb-4 ${
                        walletBalance.canProceed
                          ? 'bg-green-100 dark:bg-green-900/30 border-green-300 dark:border-green-800 text-green-800 dark:text-green-200'
                          : 'bg-red-100 dark:bg-red-900/30 border-red-300 dark:border-red-800 text-red-800 dark:text-red-200'
                      }`}>

                        {walletBalance.canProceed ? (
                          <div className="text-center">
                            <p className="text-lg font-bold">🎉 Congratulations, you qualify for free website!</p>
                          </div>
                        ) : (
                          <div>
                            <p className="font-medium mb-3">❌ Token Requirements Not Met</p>

                            {walletBalance.mainTokenResult && parseFloat(walletBalance.mainTokenResult.minimumRequired) > 0 && (
                              <div className="mb-3 space-y-1">
                                <p className="font-medium">Main token req:</p>
                                <p className="text-sm">Token req: {walletBalance.mainTokenResult.minimumRequired} {tokenRequirements?.tokenName}</p>
                                <p className="text-sm">Token balance: {walletBalance.mainTokenResult.currentBalance} {tokenRequirements?.tokenName}</p>
                              </div>
                            )}

                            {walletBalance.secondaryTokenResult &&
                             parseFloat(walletBalance.secondaryTokenResult.minimumRequired) > 0 && (
                              <div className="mb-3 space-y-1">
                                <p className="font-medium">Partner token req:</p>
                                <p className="text-sm">Token req: {walletBalance.secondaryTokenResult.minimumRequired} {walletBalance.secondaryTokenResult.tokenName}</p>
                                <p className="text-sm">Token balance: {walletBalance.secondaryTokenResult.currentBalance} {walletBalance.secondaryTokenResult.tokenName}</p>
                              </div>
                            )}
                          </div>
                        )}

                        {walletBalance.error && (
                          <p className="text-xs mt-2 opacity-75">{walletBalance.error}</p>
                        )}
                      </div>
                    ) : null}

                    {walletBalance && walletBalance.canProceed && (
                      <>
                        {errorMessage && (
                          <div className="bg-red-100 dark:bg-red-900/30 border border-red-300 dark:border-red-800 text-red-800 dark:text-red-200 rounded-md p-4 mb-4">
                            {errorMessage}
                          </div>
                        )}

                        <div className="space-y-4">
                          <div>
                            <label htmlFor="referral-code" className="block text-sm font-medium mb-1">
                              Referral Code (Optional)
                            </label>
                            <input
                              id="referral-code"
                              type="text"
                              value={referralCode}
                              onChange={(e) => setReferralCode(e.target.value)}
                              placeholder="w3txxxxx"
                              maxLength={8}
                              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                            />
                            <p className="text-xs text-gray-500 mt-1">
                              Enter a referral code if someone referred you to Web3Socials
                            </p>
                          </div>

                          <Button
                            onClick={submitTransactionHash}
                            disabled={isSubmitting}
                            className="w-full"
                          >
                            {isSubmitting ? 'Submitting...' : 'Submit for Review'}
                          </Button>
                        </div>
                      </>
                    )}
                  </>
                ) : (
                  // Referral-only mode - only show referral code form
                  <>
                    <div className="mb-4">
                      <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-3">
                        <p className="text-sm text-green-800 dark:text-green-200">
                          <strong>No token requirements configured.</strong> You can submit for approval with or without a referral code.
                        </p>
                      </div>
                    </div>

                    {errorMessage && (
                      <div className="bg-red-100 dark:bg-red-900/30 border border-red-300 dark:border-red-800 text-red-800 dark:text-red-200 rounded-md p-4 mb-4">
                        {errorMessage}
                      </div>
                    )}

                    <div className="space-y-4">
                      <div>
                        <label htmlFor="referral-code" className="block text-sm font-medium mb-1">
                          Referral Code (Optional)
                        </label>
                        <input
                          id="referral-code"
                          type="text"
                          value={referralCode}
                          onChange={(e) => setReferralCode(e.target.value)}
                          placeholder="w3txxxxx"
                          maxLength={8}
                          className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                        />
                        <p className="text-xs text-gray-500 mt-1">
                          Enter a referral code if someone referred you to Web3Socials
                        </p>
                      </div>

                      <Button
                        onClick={submitTransactionHash}
                        disabled={isSubmitting}
                        className="w-full"
                      >
                        {isSubmitting ? 'Submitting...' : 'Submit for Approval'}
                      </Button>
                    </div>
                  </>
                )}
              </>
            )}
            </div>
          </SimpleWalletValidator>
        )}
      </div>
    </div>
  );
}
