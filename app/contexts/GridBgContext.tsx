'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';

interface GridBgContextType {
  isGridBgDisabled: boolean;
  setIsGridBgDisabled: (disabled: boolean) => void;
}

const GridBgContext = createContext<GridBgContextType | undefined>(undefined);

export function GridBgProvider({ children }: { children: ReactNode }) {
  const [isGridBgDisabled, setIsGridBgDisabled] = useState(false);

  return (
    <GridBgContext.Provider
      value={{
        isGridBgDisabled,
        setIsGridBgDisabled
      }}
    >
      {children}
    </GridBgContext.Provider>
  );
}

export function useGridBg() {
  const context = useContext(GridBgContext);
  if (context === undefined) {
    throw new Error('useGridBg must be used within a GridBgProvider');
  }
  return context;
}
