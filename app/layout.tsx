import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { Toaster } from "sonner";
import SocialNavbar from "@/components/SocialNavbar";
import Providers from "@/components/Providers";
import ProfileStatusChecker from "@/components/ProfileStatusChecker";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Your Web 3 Profile",
  description: "Make your own web3 profile",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <head>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Handle wallet extension address validation errors
              (function() {
                const originalError = console.error;

                // Function to detect InvalidAddressError from wallet extensions
                const isWalletExtensionAddressError = (msg) => {
                  if (!msg) return false;
                  const msgStr = String(msg);

                  // Check for specific InvalidAddressError
                  if (msgStr.includes('InvalidAddressError') &&
                      msgStr.includes('hyagytxolymsg4xafwmfmqfhkxvpjwhfouju3kncntn2')) {
                    return true;
                  }

                  // Check for MetaMask extension errors
                  if (msgStr.includes('MetaMask extension not found') ||
                      msgStr.includes('chrome-extension://') ||
                      msgStr.includes('inpage.js')) {
                    return true;
                  }

                  return false;
                };

                // Override console.error to filter wallet extension errors
                console.error = function() {
                  const msg = Array.from(arguments).join(' ');
                  if (isWalletExtensionAddressError(msg)) {
                    // Only log in development, suppress in production
                    if (process.env.NODE_ENV === 'development') {
                      console.warn('[Wallet Extension] Filtered extension error:', msg.substring(0, 100) + '...');
                    }
                    return;
                  }
                  return originalError.apply(console, arguments);
                };

                // Global error handler for unhandled errors
                window.addEventListener('error', function(e) {
                  if (isWalletExtensionAddressError(e.message) || isWalletExtensionAddressError(e.error?.message)) {
                    if (process.env.NODE_ENV === 'development') {
                      console.warn('[Wallet Extension] Filtered window error');
                    }
                    e.preventDefault();
                    e.stopPropagation();
                  }
                }, true);

                // Global handler for unhandled promise rejections
                window.addEventListener('unhandledrejection', function(e) {
                  if (isWalletExtensionAddressError(e.reason?.message) || isWalletExtensionAddressError(String(e.reason))) {
                    if (process.env.NODE_ENV === 'development') {
                      console.warn('[Wallet Extension] Filtered promise rejection');
                    }
                    e.preventDefault();
                  }
                }, true);

                console.log('[Wallet Extension] Address validation error handler initialized');
              })();
            `,
          }}
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased overflow-x-hidden`}
      >
        <Providers>
          <ProfileStatusChecker />
          <div className="relative min-h-screen">
            <SocialNavbar />
            <div style={{ height: '48px' }}></div>
            {children}
          </div>
        </Providers>
        <Toaster />
      </body>
    </html>
  );
}

