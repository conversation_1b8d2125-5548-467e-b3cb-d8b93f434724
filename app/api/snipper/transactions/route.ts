import { NextRequest } from 'next/server';
import { createPublicClient, http, formatUnits } from 'viem';
import { cronos } from 'viem/chains';

// Create a public client for Cronos chain
const publicClient = createPublicClient({
  chain: cronos,
  transport: http()
});

interface Transaction {
  hash: string;
  from: string;
  to: string;
  value: string;
  timestamp: number;
  blockNumber: number;
}

export async function GET(request: NextRequest): Promise<Response> {
  try {
    const { searchParams } = new URL(request.url);
    const address = searchParams.get('address');
    const chainId = searchParams.get('chainId') || '25';
    const limit = parseInt(searchParams.get('limit') || '5');

    if (!address) {
      return Response.json(
        { error: 'Address parameter is required' },
        { status: 400 }
      );
    }

    // Validate address format
    if (!address.match(/^0x[a-fA-F0-9]{40}$/)) {
      return Response.json(
        { error: 'Invalid address format' },
        { status: 400 }
      );
    }

    console.log(`Fetching transactions for address: ${address} on chain: ${chainId}`);

    // Get the latest block number
    const latestBlock = await publicClient.getBlockNumber();
    console.log(`Latest block: ${latestBlock}`);

    // We'll check the last 1000 blocks for transactions
    const blocksToCheck = 1000n;
    const startBlock = latestBlock - blocksToCheck;

    const transactions: Transaction[] = [];
    let foundTransactions = 0;

    // Check blocks in reverse order (newest first)
    for (let blockNum = latestBlock; blockNum > startBlock && foundTransactions < limit; blockNum--) {
      try {
        const block = await publicClient.getBlock({
          blockNumber: blockNum,
          includeTransactions: true
        });

        if (block.transactions && Array.isArray(block.transactions)) {
          for (const tx of block.transactions) {
            // Check if transaction involves the target address
            if (typeof tx === 'object' && tx !== null && 'from' in tx && 'to' in tx) {
              const txFrom = tx.from?.toLowerCase();
              const txTo = tx.to?.toLowerCase();
              const targetAddr = address.toLowerCase();

              if (txFrom === targetAddr || txTo === targetAddr) {
                transactions.push({
                  hash: tx.hash || '',
                  from: tx.from || '',
                  to: tx.to || '',
                  value: tx.value?.toString() || '0',
                  timestamp: Number(block.timestamp),
                  blockNumber: Number(blockNum)
                });

                foundTransactions++;
                if (foundTransactions >= limit) break;
              }
            }
          }
        }
      } catch (blockError) {
        console.error(`Error fetching block ${blockNum}:`, blockError);
        // Continue with next block
        continue;
      }
    }

    // Sort transactions by block number (newest first)
    transactions.sort((a, b) => b.blockNumber - a.blockNumber);

    console.log(`Found ${transactions.length} transactions for address ${address}`);

    return Response.json({
      success: true,
      address,
      chainId,
      transactions: transactions.slice(0, limit),
      blocksChecked: Number(latestBlock - startBlock),
      latestBlock: Number(latestBlock)
    });

  } catch (error) {
    console.error('Error fetching transactions:', error);
    
    // Return a more specific error message
    let errorMessage = 'Failed to fetch transactions';
    if (error instanceof Error) {
      errorMessage = error.message;
    }

    return Response.json(
      { 
        error: errorMessage,
        success: false 
      },
      { status: 500 }
    );
  }
}
