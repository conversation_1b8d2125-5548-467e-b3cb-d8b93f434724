#!/bin/bash

# Token Holder Setup Script for Ubuntu VPS
# Run this script on your Hostinger VPS with Ubuntu + CloudPanel

set -e  # Exit on any error

echo "🚀 Starting Token Holder Setup for Ubuntu VPS..."
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root or with sudo
if [[ $EUID -eq 0 ]]; then
    print_warning "Running as root. This is fine for VPS setup."
    SUDO=""
else
    print_status "Checking sudo access..."
    if sudo -n true 2>/dev/null; then
        SUDO="sudo"
        print_success "Sudo access confirmed."
    else
        print_error "This script requires sudo access. Please run with sudo or as root."
        exit 1
    fi
fi

# Step 1: Update system packages
print_status "Updating system packages..."
$SUDO apt update
print_success "System packages updated."

# Step 2: Install Chromium and dependencies for Puppeteer
print_status "Installing Chromium browser and dependencies..."
$SUDO apt install -y \
    chromium-browser \
    fonts-liberation \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libatspi2.0-0 \
    libcups2 \
    libdbus-1-3 \
    libdrm2 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libxcomposite1 \
    libxdamage1 \
    libxfixes3 \
    libxkbcommon0 \
    libxrandr2 \
    xvfb

print_success "Chromium and dependencies installed."

# Step 3: Verify Chromium installation
print_status "Verifying Chromium installation..."
if command -v chromium-browser &> /dev/null; then
    CHROMIUM_VERSION=$(chromium-browser --version)
    print_success "Chromium installed: $CHROMIUM_VERSION"
else
    print_error "Chromium installation failed!"
    exit 1
fi

# Step 4: Check if we're in the correct directory
print_status "Checking current directory..."
if [[ ! -f "package.json" ]]; then
    print_warning "package.json not found in current directory."
    print_warning "Please navigate to your project directory first:"
    print_warning "cd /home/<USER>/htdocs/your-domain"
    print_warning "Then run this script again."
    exit 1
fi

print_success "Found package.json - we're in the project directory."

# Step 5: Install Node.js dependencies (if not already installed)
print_status "Checking Node.js dependencies..."
if npm list puppeteer &> /dev/null && npm list csv-parser &> /dev/null; then
    print_success "Required npm packages already installed."
else
    print_status "Installing missing npm packages..."
    npm install puppeteer csv-parser
    print_success "npm packages installed."
fi

# Step 6: Test Puppeteer installation
print_status "Testing Puppeteer installation..."
cat > test-puppeteer.js << 'EOF'
const puppeteer = require('puppeteer');

(async () => {
  try {
    console.log('🧪 Testing Puppeteer...');
    const browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu'
      ]
    });
    
    const page = await browser.newPage();
    await page.goto('https://example.com', { waitUntil: 'networkidle2' });
    const title = await page.title();
    console.log('✅ Puppeteer test successful! Page title:', title);
    
    await browser.close();
    process.exit(0);
  } catch (error) {
    console.error('❌ Puppeteer test failed:', error.message);
    process.exit(1);
  }
})();
EOF

if node test-puppeteer.js; then
    print_success "Puppeteer test passed!"
    rm test-puppeteer.js
else
    print_error "Puppeteer test failed!"
    rm test-puppeteer.js
    exit 1
fi

# Step 7: Database migration
print_status "Setting up database tables..."
if [[ -f "db/migrations/add_token_holder_tables.sql" ]]; then
    print_status "Found database migration file."
    print_warning "⚠️  MANUAL STEP REQUIRED:"
    print_warning "Please run the database migration manually:"
    echo ""
    echo -e "${YELLOW}mysql -u your_username -p your_database_name < db/migrations/add_token_holder_tables.sql${NC}"
    echo ""
    print_warning "Replace 'your_username' and 'your_database_name' with your actual values."
    print_warning "You'll be prompted for your MySQL password."
else
    print_error "Database migration file not found!"
    print_error "Expected: db/migrations/add_token_holder_tables.sql"
    exit 1
fi

# Step 8: Check PM2 status
print_status "Checking PM2 status..."
if command -v pm2 &> /dev/null; then
    print_success "PM2 is installed."
    print_status "Current PM2 processes:"
    pm2 list
    print_warning "⚠️  MANUAL STEP REQUIRED:"
    print_warning "After running the database migration, restart your app:"
    echo ""
    echo -e "${YELLOW}pm2 restart all${NC}"
    echo ""
else
    print_warning "PM2 not found. You may need to restart your app manually."
fi

# Step 9: Final instructions
echo ""
echo "🎉 Setup Complete!"
echo "=================="
print_success "✅ System dependencies installed"
print_success "✅ Chromium browser ready"
print_success "✅ Node.js packages installed"
print_success "✅ Puppeteer tested and working"
echo ""
print_warning "📋 REMAINING MANUAL STEPS:"
echo "1. Run the database migration (command shown above)"
echo "2. Restart your application: pm2 restart all"
echo "3. Access your admin panel: https://yourdomain.com/admin"
echo "4. Go to the 'Token Holders' tab"
echo "5. Test with Web3Tools token: 0xe68892c424E4f0EDE343F6F05c873F7b7a528048"
echo ""
print_success "🚀 Your token holder fetcher is ready to use!"

# Step 10: Environment info
echo ""
echo "📊 Environment Information:"
echo "=========================="
echo "Node.js version: $(node --version)"
echo "npm version: $(npm --version)"
echo "Chromium version: $(chromium-browser --version)"
echo "Current directory: $(pwd)"
echo "User: $(whoami)"
echo ""

print_success "Setup script completed successfully! 🎉"
