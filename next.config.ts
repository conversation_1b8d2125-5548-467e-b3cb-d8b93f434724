import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  // Disable request logging and reduce verbosity
  logging: {
    fetches: {
      fullUrl: false,
    },
  },
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          // Setting Cross-Origin-Opener-Policy to unsafe-none to fix console error
          // while still allowing wallet connections
          {
            key: 'Cross-Origin-Opener-Policy',
            value: 'unsafe-none',
          },
          // Not setting Cross-Origin-Embedder-Policy as it can cause issues with third-party resources
          // {
          //   key: 'Cross-Origin-Embedder-Policy',
          //   value: 'require-corp',
          // },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
        ],
      },
    ];
  },
};

export default nextConfig;
